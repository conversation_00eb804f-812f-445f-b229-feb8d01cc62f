<?php

namespace Database\Seeders;

use App\Models\Employee;
use App\Models\Role;
use App\Models\Branch;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first role and branch for sample data
        $role = Role::first();
        $branch = Branch::first();

        $employees = [
            [
                'nama' => '<PERSON>',
                'kode' => 'EMP001',
                'role_id' => $role?->id,
                'branch_id' => $branch?->id,
                'join_date' => '2024-01-15',
                'source' => 'Recruitment',
                'gender' => 'male',
                'tanggal_lahir' => '1990-05-20',
                'nama_asli_ktp' => 'John <PERSON>',
                'hari_off' => ['saturday', 'sunday'],
                'status' => 'active',
                'nama_rekening' => '<PERSON>',
                'no_rekening' => '**********',
                'bank' => 'BCA',
                'ukuran_seragam' => 'L',
                'ukuran_sendal' => '42',
                'ukuran_kaos' => 'L',
                'jumlah_seragam' => 2,
                'nomer_loker' => 'L001',
                'kendaraan' => 'Motor Honda',
                'deposit_seragam' => 500000,
                'tanggal_deposit' => '2024-01-15',
                'jumlah_cuti_tahunan' => 12,
                'masa_kerja_hari' => 365,
                'custom_field_1' => 'Sample custom field 1',
                'custom_field_2' => 'Sample custom field 2',
            ],
            [
                'nama' => 'Jane Smith',
                'kode' => 'EMP002',
                'role_id' => $role?->id,
                'branch_id' => $branch?->id,
                'join_date' => '2024-02-01',
                'source' => 'Internal',
                'gender' => 'female',
                'tanggal_lahir' => '1992-08-15',
                'nama_asli_ktp' => 'Jane Smith',
                'hari_off' => ['sunday'],
                'status' => 'active',
                'nama_rekening' => 'Jane Smith',
                'no_rekening' => '**********',
                'bank' => 'Mandiri',
                'ukuran_seragam' => 'M',
                'ukuran_sendal' => '38',
                'ukuran_kaos' => 'M',
                'jumlah_seragam' => 3,
                'nomer_loker' => 'L002',
                'kendaraan' => 'Mobil Toyota',
                'deposit_seragam' => 750000,
                'tanggal_deposit' => '2024-02-01',
                'jumlah_cuti_tahunan' => 12,
                'masa_kerja_hari' => 330,
                'custom_field_3' => 'Sample custom field 3',
                'custom_field_4' => 'Sample custom field 4',
            ],
            [
                'nama' => 'Ahmad Rahman',
                'kode' => 'EMP003',
                'role_id' => $role?->id,
                'branch_id' => $branch?->id,
                'join_date' => '2023-12-01',
                'source' => 'Referral',
                'gender' => 'male',
                'tanggal_lahir' => '1988-03-10',
                'nama_asli_ktp' => 'Ahmad Rahman',
                'hari_off' => ['friday', 'saturday'],
                'status' => 'active',
                'nama_rekening' => 'Ahmad Rahman',
                'no_rekening' => '**********',
                'bank' => 'BRI',
                'ukuran_seragam' => 'XL',
                'ukuran_sendal' => '43',
                'ukuran_kaos' => 'XL',
                'jumlah_seragam' => 2,
                'nomer_loker' => 'L003',
                'kendaraan' => 'Motor Yamaha',
                'deposit_seragam' => 600000,
                'tanggal_deposit' => '2023-12-01',
                'jumlah_cuti_tahunan' => 12,
                'masa_kerja_hari' => 420,
                'custom_field_5' => 'Sample custom field 5',
                'custom_field_6' => 'Sample custom field 6',
            ]
        ];

        foreach ($employees as $employee) {
            Employee::create($employee);
        }
    }
}
