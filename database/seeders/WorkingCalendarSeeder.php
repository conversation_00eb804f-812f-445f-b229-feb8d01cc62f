<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\WorkingCalendarSetting;
use Carbon\Carbon;

class WorkingCalendarSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Generate calendar for current year
        $year = date('Y');
        $startDate = Carbon::createFromDate($year, 1, 1);
        $endDate = Carbon::createFromDate($year, 12, 31);

        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            $isWeekend = $currentDate->isWeekend();
            $dayType = $isWeekend ? 'weekend' : 'weekdays';
            
            // Some holidays (example)
            $holidays = [
                $year . '-01-01', // New Year
                $year . '-08-17', // Independence Day
                $year . '-12-25', // Christmas
            ];
            
            $isHoliday = in_array($currentDate->format('Y-m-d'), $holidays);
            
            WorkingCalendarSetting::updateOrCreate(
                ['date' => $currentDate->format('Y-m-d')],
                [
                    'day_name' => $currentDate->translatedFormat('l'),
                    'week_number' => $currentDate->weekOfYear,
                    'day_type' => $dayType,
                    'is_holiday' => $isHoliday,
                    'year' => $currentDate->year,
                    'month' => $currentDate->month,
                ]
            );
            
            $currentDate->addDay();
        }
        
        $this->command->info('Working calendar seeded successfully for year ' . $year);
    }
}
