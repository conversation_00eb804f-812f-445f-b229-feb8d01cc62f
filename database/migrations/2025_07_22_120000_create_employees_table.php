<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->uuid('id')->primary();
            
            // Biodata
            $table->string('nama');
            $table->uuid('role_id')->nullable();
            $table->date('join_date')->nullable();
            $table->string('kode')->nullable();
            $table->string('source')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->date('tanggal_lahir')->nullable();
            $table->string('nama_asli_ktp')->nullable();
            $table->json('hari_off')->nullable(); // Multiple hari off (optional)
            $table->enum('status', ['active', 'inactive', 'resigned'])->default('active');
            $table->date('resign_date')->nullable();
            
            // Data Rekening
            $table->string('nama_rekening')->nullable();
            $table->string('no_rekening')->nullable();
            $table->string('bank')->nullable();
            
            // Data Perlengkapan
            $table->string('ukuran_seragam')->nullable();
            $table->string('ukuran_sendal')->nullable();
            $table->string('ukuran_kaos')->nullable();
            $table->integer('jumlah_seragam')->nullable();
            $table->string('nomer_loker')->nullable();
            $table->string('kendaraan')->nullable();
            
            // Data Deposit
            $table->decimal('deposit_seragam', 15, 2)->nullable();
            $table->date('tanggal_deposit')->nullable();
            $table->decimal('pengembalian_seragam', 15, 2)->nullable();
            $table->date('tanggal_pengembalian_deposit')->nullable();
            
            // Data Berjalan
            $table->integer('jumlah_cuti_tahunan')->nullable();
            $table->integer('masa_kerja_hari')->nullable();
            
            // Custom Fields (8 fields as shown in UI)
            $table->text('custom_field_1')->nullable();
            $table->text('custom_field_2')->nullable();
            $table->text('custom_field_3')->nullable();
            $table->text('custom_field_4')->nullable();
            $table->text('custom_field_5')->nullable();
            $table->text('custom_field_6')->nullable();
            $table->text('custom_field_7')->nullable();
            $table->text('custom_field_8')->nullable();
            
            // Relations
            $table->uuid('branch_id')->nullable();
            $table->uuid('user_id')->nullable(); // Link to users table if needed
            
            $table->softDeletes();
            $table->timestamps();
            
            // Indexes
            $table->index(['nama', 'kode']);
            $table->index(['status', 'branch_id']);
            $table->index('role_id');
            $table->index('branch_id');
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
