<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Remove masa_kerja_hari as it will be calculated dynamically
            $table->dropColumn('masa_kerja_hari');

            // Remove static custom fields as we now use dynamic custom fields
            $table->dropColumn([
                'custom_field_1',
                'custom_field_2',
                'custom_field_3',
                'custom_field_4',
                'custom_field_5',
                'custom_field_6',
                'custom_field_7',
                'custom_field_8'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Add back masa_kerja_hari
            $table->integer('masa_kerja_hari')->nullable();

            // Add back static custom fields
            $table->text('custom_field_1')->nullable();
            $table->text('custom_field_2')->nullable();
            $table->text('custom_field_3')->nullable();
            $table->text('custom_field_4')->nullable();
            $table->text('custom_field_5')->nullable();
            $table->text('custom_field_6')->nullable();
            $table->text('custom_field_7')->nullable();
            $table->text('custom_field_8')->nullable();
        });
    }
};
