<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('branches', function (Blueprint $table) {
            $table->integer('max_off_count')->default(0)->after('status')->comment('Maximum number of employees allowed to take leave on the same day');
            $table->json('approval_levels')->nullable()->after('max_off_count')->comment('Dynamic approval levels for leave requests');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('branches', function (Blueprint $table) {
            $table->dropColumn(['max_off_count', 'approval_levels']);
        });
    }
};
