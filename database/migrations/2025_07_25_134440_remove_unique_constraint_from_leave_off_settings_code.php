<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leave_off_settings', function (Blueprint $table) {
            // Drop the unique constraint on code field
            $table->dropUnique(['code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leave_off_settings', function (Blueprint $table) {
            // Add back the unique constraint on code field
            $table->unique('code');
        });
    }
};
