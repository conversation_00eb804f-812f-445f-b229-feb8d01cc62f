<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Detail Data Karyawan
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
            <a href="<?php echo e(route('employee.edit', $employee->id)); ?>" class="btn btn-primary">
                <i class="feather-edit me-2"></i>
                <span>Edit</span>
            </a>
            <a href="<?php echo e(route('employee.index')); ?>" class="btn btn-outline-primary">
                <i class="feather-arrow-left me-2"></i>
                <span>Kembali</span>
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="row">
        <div class="col-lg-12">

            <!-- Main Content Grid -->
            <div class="row">
                <!-- Biodata Section -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header py-3 bg-dark ">
                            <h5 class="mb-0 fw-bold text-white fs-16">Biodata</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="40%"><strong>Nama</strong></td>
                                    <td>: <?php echo e($employee->nama); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Role</strong></td>
                                    <td>: <?php echo e($employee->role ? $employee->role->name : '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Join Date</strong></td>
                                    <td>: <?php echo e($employee->formatted_join_date); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Kode</strong></td>
                                    <td>: <?php echo e($employee->kode ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Source</strong></td>
                                    <td>: <?php echo e($employee->source ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Gender</strong></td>
                                    <td>: <?php echo e($employee->gender_label); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Tanggal Lahir</strong></td>
                                    <td>: <?php echo e($employee->formatted_tanggal_lahir); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Nama Asli KTP</strong></td>
                                    <td>: <?php echo e($employee->nama_asli_ktp ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Hari Off</strong></td>
                                    <td>: <?php echo e($employee->hari_off_formatted); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status</strong></td>
                                    <td>: <?php echo $employee->status_badge; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Resign Date</strong></td>
                                    <td>: <?php echo e($employee->formatted_resign_date); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <!-- Data Rekening Section -->
                    <div class="col-lg-12 mb-4">
                        <div class="card h-100">
                            <div class="card-header py-3" style="background-color: #4a90a4;">
                                <h5 class="mb-0 fw-bold text-white fs-16">Data Rekening</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="40%"><strong>Nama Rekening</strong></td>
                                        <td>: <?php echo e($employee->nama_rekening ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>No Rekening</strong></td>
                                        <td>: <?php echo e($employee->no_rekening ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Bank</strong></td>
                                        <td>: <?php echo e($employee->bank ?: '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- Data Berjalan Section -->
                    <div class="col-lg-12 mb-4">
                        <div class="card h-100">
                            <div class="card-header py-3" style="background-color: #4a90a4;">
                                <h5 class="mb-0 fw-bold text-white fs-16">Data Berjalan</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="50%"><strong>Jumlah Cuti Tahunan (Tahun Ini)</strong></td>
                                        <td>: <?php echo e($employee->getJumlahCutiTahunanDinamis()); ?> hari</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Masa Kerja</strong></td>
                                        <td>: <?php echo e($employee->masa_kerja_formatted); ?> (<?php echo e($employee->masa_kerja_hari); ?>

                                            hari)</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Data Perlengkapan Section -->
                    <div class="col-lg-12 mb-4">
                        <div class="card h-100">
                            <div class="card-header py-3" style="background-color: #4a90a4; color: white;">
                                <h5 class="mb-0 fw-bold text-white fs-16">Data Perlengkapan</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="40%"><strong>Ukuran Seragam</strong></td>
                                        <td>: <?php echo e($employee->ukuran_seragam ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ukuran Sendal</strong></td>
                                        <td>: <?php echo e($employee->ukuran_sendal ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ukuran Kaos</strong></td>
                                        <td>: <?php echo e($employee->ukuran_kaos ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Jumlah Seragam</strong></td>
                                        <td>: <?php echo e($employee->jumlah_seragam ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Nomer Loker</strong></td>
                                        <td>: <?php echo e($employee->nomer_loker ?: '-'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Kendaraan</strong></td>
                                        <td>: <?php echo e($employee->kendaraan ?: '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Data Deposit Section -->
                    <div class="col-lg-12 mb-4">
                        <div class="card">
                            <div class="card-header py-3 bg-dark text-white">
                                <h5 class="mb-0 fw-bold text-white fs-16">Data Deposit</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Deposit Seragam:</strong><br>
                                        <?php echo e($employee->deposit_seragam ? 'Rp ' . number_format($employee->deposit_seragam, 0, ',', '.') : '-'); ?>

                                    </div>
                                    <div class="col-md-3">
                                        <strong>Tanggal Deposit:</strong><br>
                                        <?php echo e($employee->tanggal_deposit ? $employee->tanggal_deposit->format('d/m/Y') : '-'); ?>

                                    </div>
                                    <div class="col-md-3">
                                        <strong>Pengembalian Seragam:</strong><br>
                                        <?php echo e($employee->pengembalian_seragam ? 'Rp ' . number_format($employee->pengembalian_seragam, 0, ',', '.') : '-'); ?>

                                    </div>
                                    <div class="col-md-3">
                                        <strong>Tanggal Pengembalian Deposit:</strong><br>
                                        <?php echo e($employee->tanggal_pengembalian_deposit ? $employee->tanggal_pengembalian_deposit->format('d/m/Y') : '-'); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Fields Section -->
                <div class="col-lg-12 mb-4">
                    <div class="card">
                        <div class="card-header py-3" style="background-color: #4a90a4; color: white;">
                            <h5 class="mb-0 fw-bold text-white fs-16">Data Lain</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php if($employee->customFields && $employee->customFields->count() > 0): ?>
                                    <?php $__currentLoopData = $employee->customFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customField): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 mb-3">
                                            <strong><?php echo e($customField->field_name); ?>:</strong><br>
                                            <div class="border p-2 rounded bg-light">
                                                <?php echo e($customField->field_value ?: '-'); ?>

                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <?php
                                        $hasStaticFields = false;
                                        for ($i = 1; $i <= 8; $i++) {
                                            $field = 'custom_field_' . $i;
                                            if ($employee->$field) {
                                                $hasStaticFields = true;
                                                break;
                                            }
                                        }
                                    ?>

                                    <?php if($hasStaticFields): ?>
                                        <?php for($i = 1; $i <= 8; $i++): ?>
                                            <?php $field = 'custom_field_' . $i; ?>
                                            <?php if($employee->$field): ?>
                                                <div class="col-md-6 mb-3">
                                                    <strong>Custom Field <?php echo e($i); ?>:</strong><br>
                                                    <div class="border p-2 rounded bg-light">
                                                        <?php echo e($employee->$field); ?>

                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    <?php else: ?>
                                        <div class="col-12">
                                            <p class="text-muted text-center">Tidak ada data custom field</p>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/employee/show.blade.php ENDPATH**/ ?>