<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <?php echo e($data ?? null ? 'Edit' : 'Tambah'); ?> Role
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <div class="page-header-right-items">
            <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                <a href="<?php echo e(route('role-permission.index')); ?>" class="btn btn-light-brand">
                    <i class="feather-x me-2"></i>
                    <span>
                        Batal
                    </span>
                </a>
                <button class="btn btn-primary btnSubmit" style="width: 150px">
                    <i class="feather-save me-2"></i>
                    <span>Simpan</span>
                </button>
            </div>
        </div>
        <div class="d-md-none d-flex align-items-center">
            <a href="javascript:void(0)" class="page-header-right-open-toggle">
                <i class="feather-align-right fs-20"></i>
            </a>
        </div>
     <?php $__env->endSlot(); ?>
    <div class="row">
        <div class="col-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <?php if (isset($component)) { $__componentOriginalb24df6adf99a77ed35057e476f61e153 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb24df6adf99a77ed35057e476f61e153 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.validation-errors','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('validation-errors'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb24df6adf99a77ed35057e476f61e153)): ?>
<?php $attributes = $__attributesOriginalb24df6adf99a77ed35057e476f61e153; ?>
<?php unset($__attributesOriginalb24df6adf99a77ed35057e476f61e153); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb24df6adf99a77ed35057e476f61e153)): ?>
<?php $component = $__componentOriginalb24df6adf99a77ed35057e476f61e153; ?>
<?php unset($__componentOriginalb24df6adf99a77ed35057e476f61e153); ?>
<?php endif; ?>
                    <?php if(session('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>
                    <form class="needs-validation" novalidate action="<?php echo e(route('role-permission.store')); ?>"
                        id="formSubmit" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php if($data ?? false): ?>
                            <input type="hidden" name="id" value="<?php echo e($data->id); ?>">
                        <?php endif; ?>
                        <div class="row g-4">
                            <div class="col-4">
                                <label class="form-label">Nama Peran</label>
                                <input type="text" value="<?php echo e(old('name', $data->name ?? '')); ?>"
                                    class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="name"
                                    placeholder="Masukan name" />
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback">
                                        <?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-12 mt-4">
                                <div>
                                    <div class="d-flex align-items-center" style="gap: 20px">
                                        <div class="" style="display: inline-block">
                                            <label class="d-flex align-items-center" style="gap: 10px; cursor: pointer">
                                                <input class="checked-all"
                                                    style="height: 15px; width: 15px; border: 1px solid"
                                                    type="checkbox">
                                                Pilih semua </label>
                                        </div>
                                        <div style="cursor: pointer">
                                            <div class="btn btn-primary btn-sm collapse-all">
                                                Buka semua <i class="fas fa-chevron-down"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <div class="row">
                                            <?php
                                                $last = '';
                                                $no = 0;
                                            ?>
                                            <?php $__currentLoopData = $permission; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    $prefix = explode('.', $item->name);
                                                ?>
                                                <?php if($last != $prefix[0]): ?>
                                                    <?php if($no != 0): ?>
                                                        </table>
                                        </div>
                                        <?php endif; ?>
                                        <div class="col-md-4">
                                            <table class="table table-hover table-permission">
                                                <?php
                                                    $last = $prefix[0];
                                                ?>
                                                <thead>
                                                    <tr>
                                                        <th class="d-flex align-items-center justify-content-between">
                                                            <label class="d-flex align-items-center" style="gap: 10px">
                                                                <input class="check-all"
                                                                    style="height: 15px; width: 15px; border: 1px solid"
                                                                    type="checkbox" name="<?php echo e($last); ?>"
                                                                    id="<?php echo e(str_replace(' ', '', $last)); ?>"
                                                                    data-permission="<?php echo e(str_replace(' ', '', $last)); ?>">
                                                                Pilih semua <?php echo e($last); ?></label>
                                                            <div class="btn header-toggle btn-sm btn-primary px-2 py-1"
                                                                style="cursor: pointer; border-radius: 4px">
                                                                <i class="fas fa-chevron-down" style="color: white"></i>
                                                            </div>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <?php endif; ?>
                                                <tr class="collapse collapse-item collapse<?php echo e(str_replace(' ', '', $last)); ?>">
                                                    <?php
                                                        $has_permission = false;
                                                        try {
                                                            $has_permission = $data->hasPermissionTo($item->name);
                                                        } catch (\Throwable $th) {
                                                            $has_permission = false;
                                                        }
                                                    ?>
                                                    <td>
                                                        <label class="d-flex align-items-center" style="gap: 10px">
                                                            <input type="checkbox" class="check-all-permission"
                                                                style="height: 15px; width: 15px; border: 1px solid"
                                                                name="permission[]" id=<?php echo e($item->name); ?>"
                                                                value="<?php echo e($item->id); ?>"
                                                                data-permission="<?php echo e(str_replace(' ', '', $last)); ?>"
                                                                <?php echo e($data ?? null ? ($has_permission ? 'checked' : '') : ''); ?>>
                                                            <?php
                                                                $name = explode('.', $item->name);
                                                            ?>
                                                            <?php echo e($name[1] ?? 'List'); ?>

                                                        </label>
                                                    </td>
                                                </tr>
                                                <?php
                                                    $no++;
                                                ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php $__env->startPush('scripts'); ?>
        <script>
            $('.btnSubmit').on('click', function() {
                $('#formSubmit').submit();
            });
            $(function() {
                $('.header-toggle').on('click', function() {
                    const permission = $(this).parent().find('.check-all').attr('data-permission');
                    const child = $(`.collapse${permission}`);
                    if (child.hasClass('collapse')) {
                        child.removeClass('collapse');
                        $(this).html('<i class="fas fa-chevron-down"></i>');
                    } else {
                        child.addClass('collapse');
                        $(this).html('<i class="fas fa-chevron-up"></i>');
                    }
                });

                $('.collapse-all').on('click', function() {
                    const child = $('.collapse-item');
                    if (child.hasClass('collapse')) {
                        child.removeClass('collapse');
                        $(this).html('Buka semua <i class="fas fa-chevron-down"></i>');
                    } else {
                        child.addClass('collapse');
                        $(this).html('Tutup semua <i class="fas fa-chevron-up"></i>');
                    }
                });

                $('.check-all').on('change', function() {
                    const checked = $(this).prop("checked");
                    const permission = $(this).attr('data-permission');
                    $('input[data-permission="' + permission + '"]').prop('checked', checked);
                });

                $('.checked-all').on('change', function() {
                    const checkedAll = $('.check-all');
                    const checkedAllPermission = $('.check-all-permission');
                    checkedAll.prop('checked', this.checked);
                    checkedAllPermission.prop('checked', this.checked);
                });

                $('.check-all-permission').on('click', function() {
                    checkedAll(this);
                });

                checked();

                function checked() {
                    const parent = $('.check-all-permission');
                    $.each(parent, function(i, v) {
                        checkedAll($(v));
                    })
                }

                function checkedAll(element) {
                    let permission = $(element).attr('data-permission');
                    checkPermission = $('.check-all-permission[data-permission="' + permission + '"]');
                    checkedPermission = $('.check-all-permission[data-permission="' + permission + '"]:checked');

                    if ($(this).prop("checked") != true) {
                        $('.check-all[data-permission="' + permission + '"]').prop("checked", false);
                    }

                    if (checkPermission.length == checkedPermission.length) {
                        $('.check-all[data-permission="' + permission + '"]').prop("checked", true);
                    }
                }
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/role-permission/create-update.blade.php ENDPATH**/ ?>