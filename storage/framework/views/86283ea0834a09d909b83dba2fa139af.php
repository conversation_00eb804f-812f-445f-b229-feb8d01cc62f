<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Tambah Pengajuan Absen
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <a href="<?php echo e(route('attendance-request.index')); ?>" class="btn btn-secondary">
            <i class="feather-arrow-left me-2"></i>Kembali
        </a>
     <?php $__env->endSlot(); ?>
    <div class="row">
        <div class="col-lg-8">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Form Pengajuan Absen</h5>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(route('attendance-request.store')); ?>" method="POST" enctype="multipart/form-data" id="attendanceRequestForm">
                            <?php echo csrf_field(); ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="branch_id" class="form-label">Cabang <span class="text-danger">*</span></label>
                                        <select class="form-select select-2 <?php $__errorArgs = ['branch_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="branch_id" name="branch_id" required>
                                            <option value="">Pilih Cabang</option>
                                            <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($branch->id); ?>" <?php echo e(old('branch_id') == $branch->id ? 'selected' : ''); ?>>
                                                    <?php echo e($branch->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php $__errorArgs = ['branch_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="employee_user_id" class="form-label">Nama Karyawan <span class="text-danger">*</span></label>
                                        <select class="form-select <?php $__errorArgs = ['employee_user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="employee_user_id" name="employee_user_id" required disabled>
                                            <option value="">Pilih cabang terlebih dahulu</option>
                                        </select>
                                        <?php $__errorArgs = ['employee_user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="attendance_date" class="form-label">Tanggal Absen <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control <?php $__errorArgs = ['attendance_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="attendance_date" name="attendance_date" 
                                               value="<?php echo e(old('attendance_date')); ?>" 
                                               min="<?php echo e(date('Y-m-d')); ?>" required>
                                        <?php $__errorArgs = ['attendance_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="attendance_type_id" class="form-label">Jenis Absen <span class="text-danger">*</span></label>
                                        <select class="form-select <?php $__errorArgs = ['attendance_type_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="attendance_type_id" name="attendance_type_id" required>
                                            <option value="">Pilih Jenis Absen</option>
                                            <?php $__currentLoopData = $attendanceTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($type->id); ?>" <?php echo e(old('attendance_type_id') == $type->id ? 'selected' : ''); ?>>
                                                    <?php echo e($type->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php $__errorArgs = ['attendance_type_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="reason" class="form-label">Keterangan/Alasan <span class="text-danger">*</span></label>
                                <textarea class="form-control <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="reason" name="reason" rows="4" 
                                          placeholder="Masukkan keterangan atau alasan pengajuan absen..." required><?php echo e(old('reason')); ?></textarea>
                                <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3">
                                <label for="attachment" class="form-label">Lampiran (Opsional)</label>
                                <input type="file" class="form-control <?php $__errorArgs = ['attachment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="attachment" name="attachment" 
                                       accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                                <div class="form-text">
                                    Format yang diizinkan: JPG, JPEG, PNG, PDF, DOC, DOCX. Maksimal 2MB.
                                </div>
                                <?php $__errorArgs = ['attachment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="<?php echo e(route('attendance-request.index')); ?>" class="btn btn-secondary">
                                    <i class="feather-arrow-left me-2"></i>Kembali
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="feather-save me-2"></i>Simpan Pengajuan
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Informasi</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                            <ul class="mb-0">
                                <li><strong>Cabang:</strong> Pilih cabang tempat karyawan bekerja</li>
                                <li><strong>Nama Karyawan:</strong> Pilih karyawan yang akan diajukan absennya</li>
                                <li><strong>Tanggal Absen:</strong> Tanggal yang akan diabsen (minimal hari ini)</li>
                                <li><strong>Jenis Absen:</strong> Pilih jenis absen sesuai kebutuhan</li>
                                <li><strong>Keterangan:</strong> Jelaskan alasan pengajuan absen</li>
                                <li><strong>Lampiran:</strong> Upload dokumen pendukung jika ada (opsional)</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">Catatan Penting:</h6>
                            <ul class="mb-0">
                                <li>Pengajuan hanya dapat dibuat untuk tanggal hari ini atau yang akan datang</li>
                                <li>Setiap karyawan hanya dapat memiliki satu pengajuan per tanggal</li>
                                <li>Pengajuan yang sudah disetujui/ditolak tidak dapat diedit</li>
                                <li>Lampiran maksimal 2MB dengan format yang diizinkan</li>
                            </ul>
                        </div>
                    </div>
                </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Load employees when branch is selected
    $('#branch_id').change(function() {
        let branchId = $(this).val();
        let employeeSelect = $('#employee_user_id');
        
        if (branchId) {
            employeeSelect.prop('disabled', true).html('<option value="">Memuat...</option>');
            
            $.ajax({
                url: "<?php echo e(route('attendance-request.getEmployeesByBranch')); ?>",
                type: 'GET',
                data: { branch_id: branchId },
                success: function(response) {
                    employeeSelect.html('<option value="">Pilih Karyawan</option>');
                    
                    if (response.length > 0) {
                        $.each(response, function(index, employee) {
                            employeeSelect.append(`<option value="${employee.id}">${employee.nama} (${employee.email})</option>`);
                        });
                        employeeSelect.prop('disabled', false);
                    } else {
                        employeeSelect.html('<option value="">Tidak ada karyawan aktif di cabang ini</option>');
                    }
                },
                error: function(xhr) {
                    employeeSelect.html('<option value="">Error memuat data karyawan</option>');
                    if (xhr.status === 403) {
                        Swal.fire('Error!', 'Anda tidak memiliki akses ke cabang ini', 'error');
                    } else {
                        Swal.fire('Error!', 'Terjadi kesalahan saat memuat data karyawan', 'error');
                    }
                }
            });
        } else {
            employeeSelect.prop('disabled', true).html('<option value="">Pilih cabang terlebih dahulu</option>');
        }
    });

    // Form validation
    $('#attendanceRequestForm').submit(function(e) {
        let isValid = true;
        let errorMessage = '';

        // Check if all required fields are filled
        if (!$('#branch_id').val()) {
            isValid = false;
            errorMessage += 'Cabang harus dipilih\n';
        }

        if (!$('#employee_user_id').val()) {
            isValid = false;
            errorMessage += 'Karyawan harus dipilih\n';
        }

        if (!$('#attendance_date').val()) {
            isValid = false;
            errorMessage += 'Tanggal absen harus diisi\n';
        }

        if (!$('#attendance_type_id').val()) {
            isValid = false;
            errorMessage += 'Jenis absen harus dipilih\n';
        }

        if (!$('#reason').val().trim()) {
            isValid = false;
            errorMessage += 'Keterangan harus diisi\n';
        }

        // Check file size if attachment is uploaded
        let fileInput = $('#attachment')[0];
        if (fileInput.files.length > 0) {
            let fileSize = fileInput.files[0].size / 1024 / 1024; // Convert to MB
            if (fileSize > 2) {
                isValid = false;
                errorMessage += 'Ukuran file lampiran tidak boleh lebih dari 2MB\n';
            }
        }

        if (!isValid) {
            e.preventDefault();
            Swal.fire('Validasi Error!', errorMessage, 'error');
            return false;
        }

        // Show loading
        $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="feather-loader me-2"></i>Menyimpan...');
    });

    // File upload preview
    $('#attachment').change(function() {
        let file = this.files[0];
        if (file) {
            let fileSize = file.size / 1024 / 1024; // Convert to MB
            let fileName = file.name;
            
            if (fileSize > 2) {
                Swal.fire('Error!', 'Ukuran file tidak boleh lebih dari 2MB', 'error');
                $(this).val('');
                return;
            }
            
            // Show file info
            let fileInfo = `File dipilih: ${fileName} (${fileSize.toFixed(2)} MB)`;
            $(this).next('.form-text').text(fileInfo);
        }
    });

    // Restore old values if validation fails
    <?php if(old('branch_id')): ?>
        $('#branch_id').trigger('change');
        setTimeout(function() {
            $('#employee_user_id').val('<?php echo e(old('employee_user_id')); ?>');
        }, 1000);
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/attendance-request/create.blade.php ENDPATH**/ ?>