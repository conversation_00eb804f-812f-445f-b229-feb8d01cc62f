<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Detail Pengajuan Absen
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <div class="d-flex align-items-center gap-2">
            <a href="<?php echo e(route('attendance-request.index')); ?>" class="btn btn-secondary">
                <i class="feather-arrow-left me-2"></i>Kembali
            </a>

            <?php if($attendanceRequest->canBeEdited() && canPermission('Pengajuan Absen.Update')): ?>
                <a href="<?php echo e(route('attendance-request.edit', $attendanceRequest->id)); ?>" class="btn btn-warning">
                    <i class="feather-edit me-2"></i>
                    <span>Edit</span>
                </a>
            <?php endif; ?>

            <?php if($attendanceRequest->canBeProcessed() && canPermission('Pengajuan Absen.Approve')): ?>
                <button type="button" class="btn btn-success" id="approveBtn" data-id="<?php echo e($attendanceRequest->id); ?>">
                    <i class="feather-check me-2"></i>
                    <span>Setujui</span>
                </button>
                <button type="button" class="btn btn-danger" id="rejectBtn" data-id="<?php echo e($attendanceRequest->id); ?>">
                    <i class="feather-x me-2"></i>
                    <span>Tolak</span>
                </button>
            <?php endif; ?>
        </div>
     <?php $__env->endSlot(); ?>
    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi Pengajuan</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Nama Karyawan:</label>
                                <div class="form-control-plaintext">
                                    <?php echo e($attendanceRequest->employee->nama ?? '-'); ?>

                                    <?php if($attendanceRequest->employee->kode ?? false): ?>
                                        <small class="text-muted">(<?php echo e($attendanceRequest->employee->kode); ?>)</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Role Karyawan:</label>
                                <div class="form-control-plaintext"><?php echo e($attendanceRequest->employee->role->name ?? '-'); ?>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Cabang:</label>
                                <div class="form-control-plaintext"><?php echo e($attendanceRequest->branch->name ?? '-'); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tanggal Absen:</label>
                                <div class="form-control-plaintext"><?php echo e($attendanceRequest->attendance_date_formatted); ?>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Jenis Absen:</label>
                                <div class="form-control-plaintext">
                                    <?php echo e($attendanceRequest->attendanceType->name ?? '-'); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <div class="form-control-plaintext"><?php echo $attendanceRequest->status_badge; ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Keterangan/Alasan:</label>
                        <div class="form-control-plaintext border rounded p-3 bg-light">
                            <?php echo e($attendanceRequest->reason); ?>

                        </div>
                    </div>

                    <?php if($attendanceRequest->attachment): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Lampiran:</label>
                            <div class="form-control-plaintext">
                                <div class="d-flex align-items-center">
                                    <i class="feather-paperclip me-2"></i>
                                    <a href="<?php echo e(asset($attendanceRequest->attachment)); ?>" target="_blank"
                                        class="text-primary">
                                        <?php echo e(basename($attendanceRequest->attachment)); ?>

                                    </a>
                                    <span class="badge bg-info ms-2">
                                        <?php echo e(strtoupper(pathinfo($attendanceRequest->attachment, PATHINFO_EXTENSION))); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($attendanceRequest->rejection_reason): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold text-danger">Alasan Penolakan:</label>
                            <div class="form-control-plaintext border rounded p-3 bg-danger-subtle text-danger">
                                <?php echo e($attendanceRequest->rejection_reason); ?>

                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Riwayat Pengajuan</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item mb-4">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Pengajuan Dibuat</h6>
                                <p class="timeline-text">
                                    Oleh: <?php echo e($attendanceRequest->user->nama ?? '-'); ?><br>
                                    Tanggal: <?php echo e($attendanceRequest->created_at_formatted); ?>

                                </p>
                            </div>
                        </div>

                        <?php if($attendanceRequest->approved_by): ?>
                            <div class="timeline-item">
                                <div
                                    class="timeline-marker <?php echo e($attendanceRequest->status === 'approved' ? 'bg-success' : 'bg-danger'); ?>">
                                </div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">
                                        <?php echo e($attendanceRequest->status === 'approved' ? 'Pengajuan Disetujui' : 'Pengajuan Ditolak'); ?>

                                    </h6>
                                    <p class="timeline-text">
                                        Oleh: <?php echo e($attendanceRequest->approvedBy->nama ?? '-'); ?><br>
                                        Tanggal: <?php echo e($attendanceRequest->approved_at_formatted); ?>

                                    </p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if($attendanceRequest->status === 'pending'): ?>
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">Status Menunggu</h6>
                            <p class="mb-0">Pengajuan ini masih menunggu persetujuan dari atasan.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <?php $__env->startPush('modals'); ?>
        <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="rejectModalLabel">Tolak Pengajuan Absen</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="rejectForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="rejection_reason" class="form-label">Alasan Penolakan <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4"
                                    placeholder="Masukkan alasan penolakan..." required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-danger">Tolak Pengajuan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Approve functionality
            $('#approveBtn').click(function() {
                let id = $(this).data('id');

                Swal.fire({
                    title: 'Konfirmasi',
                    text: 'Apakah Anda yakin ingin menyetujui pengajuan ini?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#28a745',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Ya, Setujui',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            url: "<?php echo e(url('attendance-request')); ?>/" + id + "/approve",
                            type: 'POST',
                            data: {
                                _token: "<?php echo e(csrf_token()); ?>"
                            },
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire('Berhasil!', response.message, 'success')
                                        .then(() => {
                                            location.reload();
                                        });
                                } else {
                                    Swal.fire('Error!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                Swal.fire('Error!',
                                    'Terjadi kesalahan saat memproses permintaan',
                                    'error');
                            }
                        });
                    }
                });
            });

            // Reject functionality
            $('#rejectBtn').click(function() {
                $('#rejectModal').modal('show');
            });

            $('#rejectForm').submit(function(e) {
                e.preventDefault();
                let id = $('#rejectBtn').data('id');

                $.ajax({
                    url: "<?php echo e(url('attendance-request')); ?>/" + id + "/reject",
                    type: 'POST',
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>",
                        rejection_reason: $('#rejection_reason').val()
                    },
                    success: function(response) {
                        if (response.status) {
                            $('#rejectModal').modal('hide');
                            Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function(xhr) {
                        Swal.fire('Error!', 'Terjadi kesalahan saat memproses permintaan',
                            'error');
                    }
                });
            });

            // Delete functionality
            $('#deleteBtn').click(function() {
                let id = $(this).data('id');

                Swal.fire({
                    title: 'Konfirmasi',
                    text: 'Apakah Anda yakin ingin menghapus pengajuan ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Ya, Hapus',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            url: "<?php echo e(url('attendance-request')); ?>/" + id,
                            type: 'DELETE',
                            data: {
                                _token: "<?php echo e(csrf_token()); ?>"
                            },
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire('Berhasil!', response.message, 'success')
                                        .then(() => {
                                            window.location.href =
                                                "<?php echo e(route('attendance-request.index')); ?>";
                                        });
                                } else {
                                    Swal.fire('Error!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                Swal.fire('Error!',
                                    'Terjadi kesalahan saat menghapus data', 'error'
                                    );
                            }
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/attendance-request/show.blade.php ENDPATH**/ ?>