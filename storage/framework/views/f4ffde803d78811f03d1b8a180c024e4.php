<div class="page-header">
    <div class="page-header-left d-flex align-items-center">
        <div class="page-header-title">
            <h5 class="m-b-10">
                <?php echo e($slot); ?>

            </h5>
        </div>
    </div>
    <?php if(isset($headerRight)): ?>
        <?php if (isset($component)) { $__componentOriginal372f41e8127de977a7c293906067ac0f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal372f41e8127de977a7c293906067ac0f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.header-right','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('header-right'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <?php echo e($headerRight); ?>

         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal372f41e8127de977a7c293906067ac0f)): ?>
<?php $attributes = $__attributesOriginal372f41e8127de977a7c293906067ac0f; ?>
<?php unset($__attributesOriginal372f41e8127de977a7c293906067ac0f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal372f41e8127de977a7c293906067ac0f)): ?>
<?php $component = $__componentOriginal372f41e8127de977a7c293906067ac0f; ?>
<?php unset($__componentOriginal372f41e8127de977a7c293906067ac0f); ?>
<?php endif; ?>
    <?php endif; ?>
</div>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/components/header.blade.php ENDPATH**/ ?>