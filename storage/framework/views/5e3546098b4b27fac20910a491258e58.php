<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <?php echo e(isset($data) ? 'Edit' : 'Tambah'); ?> Jenis Absen
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <a href="<?php echo e(route('attendance-type-setting.index')); ?>" class="btn btn-outline-primary">
            <i class="feather-arrow-left me-2"></i>
            <span>Kembali</span>
        </a>
     <?php $__env->endSlot(); ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <form action="<?php echo e(route('attendance-type-setting.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php if(isset($data)): ?>
                            <input type="hidden" name="id" value="<?php echo e($data->id); ?>">
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Jenis Absen <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="name" name="name"
                                           value="<?php echo e(old('name', $data->name ?? '')); ?>"
                                           placeholder="Masukkan nama jenis absen" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Kode</label>
                                    <input type="text" class="form-control"
                                           id="code" name="code"
                                           value="<?php echo e(isset($data) ? $data->code : ''); ?>"
                                           readonly>
                                    <small class="text-muted">Kode otomatis dibuat dari nama jenis absen</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reduction_days_leave" class="form-label">Pengurangan Hari Cuti <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['reduction_days_leave'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="reduction_days_leave" name="reduction_days_leave" 
                                           value="<?php echo e(old('reduction_days_leave', $data->reduction_days_leave ?? 0)); ?>" 
                                           placeholder="0" min="0" required>
                                    <?php $__errorArgs = ['reduction_days_leave'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="form-text">Jumlah hari yang dikurangi dari jatah cuti</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reduction_days_off" class="form-label">Pengurangan Hari Off <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['reduction_days_off'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="reduction_days_off" name="reduction_days_off" 
                                           value="<?php echo e(old('reduction_days_off', $data->reduction_days_off ?? 0)); ?>" 
                                           placeholder="0" min="0" required>
                                    <?php $__errorArgs = ['reduction_days_off'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="form-text">Jumlah hari yang dikurangi dari jatah libur</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="additional_days" class="form-label">Tambahan Jika di Weekend/Hari Besar <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['additional_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="additional_days" name="additional_days" 
                                           value="<?php echo e(old('additional_days', $data->additional_days ?? 0)); ?>" 
                                           placeholder="0" min="0" required>
                                    <?php $__errorArgs = ['additional_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="form-text">Jumlah hari tambahan yang diberikan</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?php echo e(route('attendance-type-setting.index')); ?>" class="btn btn-outline-secondary">
                                <i class="feather-x me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                <?php echo e(isset($data) ? 'Update' : 'Simpan'); ?>

                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                        <ul class="mb-0">
                            <li><strong>Nama:</strong> Nama jenis absen (contoh: Sakit, Alpha, Izin)</li>
                            <li><strong>Kode:</strong> Kode unik untuk identifikasi</li>
                            <li><strong>Pengurangan Hari Cuti:</strong> Berapa hari cuti yang dikurangi</li>
                            <li><strong>Pengurangan Hari Off:</strong> Berapa hari libur yang dikurangi</li>
                            <li><strong>Tambahan Jika di Weekend/Hari Besar:</strong> Berapa hari tambahan yang diberikan</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Contoh Pengaturan:</h6>
                        <ul class="mb-0">
                            <li><strong>Sakit:</strong> Mengurangi 1 hari cuti, 0 hari libur</li>
                            <li><strong>Alpha:</strong> Mengurangi 1 hari cuti, 1 hari libur</li>
                            <li><strong>Izin:</strong> Mengurangi 1 hari cuti, 0 hari libur</li>
                            <li><strong>Lembur:</strong> Menambah 1 hari libur</li>
                        </ul>
                    </div>
                    
                    <?php if(isset($data)): ?>
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">Informasi Data:</h6>
                        <ul class="mb-0">
                            <li><strong>Dibuat:</strong> <?php echo e($data->created_at->translatedFormat('d F Y H:i')); ?></li>
                            <?php if($data->updated_at != $data->created_at): ?>
                            <li><strong>Diupdate:</strong> <?php echo e($data->updated_at->translatedFormat('d F Y H:i')); ?></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            $(document).ready(function() {
                // Auto generate code from name
                $('#name').on('input', function() {
                    if (!$('#code').val()) {
                        let name = $(this).val();
                        let code = name.toUpperCase()
                                      .replace(/[^A-Z0-9\s]/g, '')
                                      .replace(/\s+/g, '_')
                                      .substring(0, 10);
                        $('#code').val(code);
                    }
                });

                // Auto preview code from name
                $('#name').on('input', function() {
                    let name = $(this).val();
                    let baseCode = name.toUpperCase()
                                      .replace(/[^A-Z0-9]/g, '')
                                      .substring(0, 8);
                    if (!baseCode) {
                        baseCode = 'ATTEND';
                    }
                    $('#code').val(baseCode + '...');
                });

                // Form validation
                $('form').on('submit', function(e) {
                    let isValid = true;

                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'error',
                            title: 'Form Tidak Lengkap!',
                            text: 'Mohon lengkapi semua field yang wajib diisi'
                        });
                    }
                });

                // Clear validation on input change
                $('input, select').on('change', function() {
                    $(this).removeClass('is-invalid');
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/attendance-type-setting/create-update.blade.php ENDPATH**/ ?>