<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Data Karyawan
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 

     <?php $__env->endSlot(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="" style="width: 300px">
                            <input type="text" class="form-control" placeholder="Cari nama karyawan/kode/role/cabang"
                                oninput="handleSearch(event)" style="width: 100%" />
                        </div>
                        <div class="ms-md-auto mt-md-0 mt-3">
                            
                            <div>
                                <a href="<?php echo e(route('employee.create')); ?>" class="btn btn-primary">
                                    <i class="feather-plus me-2"></i>
                                    <span>Tambah Karyawan</span>
                                </a>
                            </div>
                            
                        </div>
                    </div>
                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Nama</th>
                                    <th>Kode</th>
                                    <th>Role</th>
                                    <th>Cabang</th>
                                    <th>Tanggal Bergabung</th>
                                    <th>Status</th>
                                    <th>Gender</th>
                                    <th>Hari Off</th>
                                    <th style="width: 12px">Action</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('libs.datatable', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php $__env->startPush('scripts'); ?>
        <script>
            let table;
            $(document).ready(function() {
                table = $('#example').DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "<?php echo e(route('employee.dataTable')); ?>",
                        type: "POST",
                        data: function(d) {
                            d._token = "<?php echo e(csrf_token()); ?>";
                            d.keyword = $('.form-control').val();
                        }
                    },
                    columns: [{
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'nama',
                            name: 'nama'
                        },
                        {
                            data: 'kode',
                            name: 'kode'
                        },
                        {
                            data: 'role_name',
                            name: 'role.name'
                        },
                        {
                            data: 'branch_name',
                            name: 'branch.name'
                        },
                        {
                            data: 'formatted_join_date',
                            name: 'join_date'
                        },
                        {
                            data: 'status_badge',
                            name: 'status'
                        },
                        {
                            data: 'gender_label',
                            name: 'gender'
                        },
                        {
                            data: 'hari_off_formatted',
                            name: 'hari_off'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                });

                // Delete functionality
                $(document).on('click', '.deleteData', function() {
                    let id = $(this).data('id');
                    let input = $(this).data('input');

                    Swal.fire({
                        title: 'Apakah Anda yakin?',
                        text: `Data karyawan "${input.nama}" akan dihapus!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Ya, hapus!',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $.ajax({
                                url: "<?php echo e(route('employee.destroy', ':id')); ?>".replace(':id',
                                    id),
                                type: 'DELETE',
                                data: {
                                    _token: "<?php echo e(csrf_token()); ?>"
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat menghapus data', 'error'
                                        );
                                }
                            });
                        }
                    });
                });
            });

            function handleSearch(event) {
                table.ajax.reload();
            }
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/employee/index.blade.php ENDPATH**/ ?>