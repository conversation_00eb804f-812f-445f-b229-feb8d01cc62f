<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <?php echo e($data ?? null ? 'Edit' : 'Tambah'); ?> Pengguna
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <div class="page-header-right-items">
            <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                <a href="<?php echo e(route('pengguna.index')); ?>" class="btn btn-light-brand">
                    <i class="feather-x me-2"></i>
                    <span>
                        Batal
                    </span>
                </a>
                <button class="btn btn-primary btnSubmit" style="width: 150px">
                    <i class="feather-save me-2"></i>
                    <span>Simpan</span>
                </button>
            </div>
        </div>
        <div class="d-md-none d-flex align-items-center">
            <a href="javascript:void(0)" class="page-header-right-open-toggle">
                <i class="feather-align-right fs-20"></i>
            </a>
        </div>
     <?php $__env->endSlot(); ?>
    <div class="row">
        <div class="col-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <?php if (isset($component)) { $__componentOriginalb24df6adf99a77ed35057e476f61e153 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb24df6adf99a77ed35057e476f61e153 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.validation-errors','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('validation-errors'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb24df6adf99a77ed35057e476f61e153)): ?>
<?php $attributes = $__attributesOriginalb24df6adf99a77ed35057e476f61e153; ?>
<?php unset($__attributesOriginalb24df6adf99a77ed35057e476f61e153); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb24df6adf99a77ed35057e476f61e153)): ?>
<?php $component = $__componentOriginalb24df6adf99a77ed35057e476f61e153; ?>
<?php unset($__componentOriginalb24df6adf99a77ed35057e476f61e153); ?>
<?php endif; ?>
                    <?php if(session('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>
                    <form
                        class="needs-validation"
                        novalidate
                        action="<?php echo e(route('pengguna.store')); ?>"
                        id="formSubmit" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php if($data ?? false): ?>
                            <input type="hidden" name="id" value="<?php echo e($data->id); ?>">
                        <?php endif; ?>
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label">Nama</label>
                                <input type="text" class="form-control" name="nama" placeholder="Nama" required value="<?php echo e(old('nama', ($data->nama ?? null))); ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" placeholder="Email" required value="<?php echo e(old('email', ($data->email ?? null))); ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Role</label>
                                <?php
                                    $role = App\Models\Role::pluck('name', 'id');
                                ?>
                                <select class="form-control select2" name="role_id" data-select2-selector="icon">
                                    <option value="">Pilih Role</option>
                                    <?php $__currentLoopData = $role; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" <?php echo e(old('role_id', ($data->role_id ?? null)) == $key ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="row g-4">
                            <?php
                                $branches = App\Models\Branch::active()->pluck('name', 'id');
                                $branch_ids = isset($data) ? ($data->userAccessBranches->pluck('branch_id')->toArray() ?? []) : [];
                                $branch_ids = old('branch_ids', $branch_ids);

                                // Debug information (remove in production)
                                if(isset($data)) {
                                    \Log::info('User Edit - Branch IDs:', $branch_ids);
                                    \Log::info('User Access Branches:', $data->userAccessBranches->toArray());
                                }
                            ?>
                            <div class="col-md-12">
                                <div class="card border-primary mt-4">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0 text-white">
                                            <i class="feather-map-pin me-2"></i>
                                            Akses Cabang <span class="text-warning">*</span>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <select class="form-select select2" name="branch_ids[]" id="branch_ids" multiple required>
                                            <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(in_array($key, $branch_ids) ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <small class="form-text text-muted mt-2 d-block">
                                            <i class="feather-info me-1"></i>
                                            Pilih satu atau lebih cabang yang dapat diakses oleh pengguna ini. Pengguna hanya dapat mengakses data dari cabang yang dipilih.
                                        </small>
                                        <?php $__errorArgs = ['branch_ids'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback d-block mt-2"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                                        <?php if(isset($data) && count($branch_ids) > 0): ?>
                                            <div class="mt-2">
                                                <small class="text-success">
                                                    <i class="feather-check-circle me-1"></i>
                                                    Saat ini memiliki akses ke <?php echo e(count($branch_ids)); ?> cabang
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="password"
                                    placeholder="Masukan Password" <?php echo e(isset($data) ? '' : 'required'); ?>>
                                <?php if(isset($data)): ?>
                                    <small class="form-text text-muted">Kosongkan jika tidak ingin mengubah password</small>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Konfirmasi Password</label>
                                <input type="password" class="form-control" name="confirm_password"
                                    placeholder="Masukan Konfirmasi Password">
                            </div>
                            
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php $__env->startPush('scripts'); ?>
        <script>
            $(document).ready(function() {
                // Initialize Select2 for branch selection with enhanced options
                $('#branch_ids').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    placeholder: 'Pilih cabang yang dapat diakses...',
                    allowClear: false,
                    closeOnSelect: false,
                    tags: false
                });

                // Form submission handler
                $('.btnSubmit').on('click', function() {
                    // Validate branch selection
                    const selectedBranches = $('#branch_ids').val();
                    if (!selectedBranches || selectedBranches.length === 0) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Akses Cabang Diperlukan!',
                            text: 'Silakan pilih minimal satu cabang yang dapat diakses oleh pengguna ini.'
                        });
                        return false;
                    }

                    $('#formSubmit').submit();
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/pengguna/create-update.blade.php ENDPATH**/ ?>