<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Peran & Hak Akses
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 

     <?php $__env->endSlot(); ?>
    <nav>
        <div class="nav nav-tabs fs-14 fw-bold" id="nav-tab" role="tablist">
            <button class="nav-link active" id="role-page-tab" data-bs-toggle="tab" data-bs-target="#role-page"
                type="button" role="tab" aria-controls="role-page" aria-selected="true">Peran</button>
            <button class="nav-link fs-14 fw-bold" id="nav-role-page-tab" data-bs-toggle="tab"
                data-bs-target="#nav-role-page" type="button" role="tab" aria-controls="nav-role-page"
                aria-selected="false">Hak Akses</button>
        </div>
    </nav>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="tab-content" id="nav-tabContent">
                        <div class="tab-pane fade show active" id="role-page" role="tabpanel"
                            aria-labelledby="role-page-tab" tabindex="0">
                            <div class="d-flex align-items-center flex-wrap">
                                <div class="" style="width: 300px">
                                    <input type="text" class="form-control" placeholder="Cari nama user/unit/role"
                                        oninput="handleSearch(event)" style="width: 100%" />
                                </div>
                                <div class="ms-md-auto mt-md-0 mt-3">
                                    <?php if(canPermission('Role & Permission.Create')): ?>
                                    <div>
                                        <a href="<?php echo e(route('role-permission.create')); ?>" class="btn btn-primary">
                                            <i class="feather-plus me-2"></i>
                                            <span>Tambah Peran</span>
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="table-responsive mt-4">
                                <table class="table table-hover" id="example">
                                    <thead>
                                        <tr>
                                            <th style="width: 12px">No</th>
                                            <th>Nama</th>
                                            <th style="width: 300px">Created At</th>
                                            <th style="width: 150px">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="nav-role-page" role="tabpanel"
                            aria-labelledby="nav-role-page-tab" tabindex="0">
                            <div class="d-flex align-items-center flex-wrap">
                                <div class="" style="width: 300px">
                                </div>
                                <div class="ms-md-auto mt-md-0 mt-3">
                                    <?php if(canPermission('Role & Permission.Create')): ?>
                                    <div>
                                        <div role="button" class="btn btn-primary" data-bs-toggle="modal"
                                            data-bs-target="#modalInputHakAkses">
                                            <i class="feather-plus me-2"></i>
                                            <span>Tambah Hak Akses</span>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="table-responsive mt-4">
                                <table class="table table-hover" id="permission_table">
                                    <thead>
                                        <tr>
                                            <th style="width: 12px">No</th>
                                            <th>Nama Hak Akses</th>
                                            <th style="width: 300px">Created At</th>
                                            <th style="width: 150px">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('modals'); ?>
        <div class="modal fade-scale" id="modalInputHakAkses" tabindex="-1" data-bs-backdrop="static"
            data-bs-keyboard="false" aria-hidden="true" data-bs-dismiss="ou">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="d-flex flex-column mb-0">
                            <span class="fs-18 fw-bold mb-1">Hak Akses</span>
                            <small class="d-block fs-11 fw-normal text-muted">
                                Input Hak Akses
                            </small>
                        </h2>
                        <a href="javascript:void(0)" class="avatar-text avatar-md bg-soft-danger close-icon"
                            data-bs-dismiss="modal">
                            <i class="feather-x text-danger"></i>
                        </a>
                    </div>
                    <form id="formInputHakAkses" action="<?php echo e(route('role-permission.store-permission')); ?>" method="POST">
                        <div class="modal-body">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="permission_id" id="permission_id">
                            <div class="form-group mb-4">
                                <label for="name" class="form-label">Nama Hak Akses</label>
                                <input type="text" class="form-control" id="name" name="name"
                                    placeholder="Nama">
                            </div>
                        </div>
                        <div class="modal-footer d-flex align-items-center justify-content-between gap-3 w-100">
                            <button type="button" class="btn btn-outline-secondary"
                                data-bs-dismiss="modal">Batal</button>
                            <button class="btn btn-primary">Simpan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php $__env->stopPush(); ?>

    <?php echo $__env->make('libs.datatable', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php $__env->startPush('scripts'); ?>
        <script>
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {};
            const table_permission = $("#permission_table").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `${base_url}/role-permission/dataTable/permission`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "name",
                        data: "name",
                        orderable: false,
                    },
                    {
                        name: "created_at",
                        data: "created_at",
                        orderable: false,
                    },
                    {
                        name: "action",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            const table = $("#example").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `${base_url}/role-permission/dataTable`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "name",
                        data: "name",
                        orderable: false,
                    },
                    {
                        name: "created_at",
                        data: "created_at",
                        orderable: false,
                    },
                    {
                        name: "action",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    table.draw();
                }, 500);
            }

            $(document).on('click', '.deleteData', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const nama = dataInput.name;
                const urlTarget = `${base_url}/role-permission/${id}`
                await deleteDataTable(nama, urlTarget, table)
            }).on('click', '.deleteDataPermission', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const nama = dataInput.name;
                const urlTarget = `${base_url}/role-permission/${id}/permission`
                await deleteDataTable(nama, urlTarget, table_permission)
            }).on("click", '.editInput', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                formInputHakAkses.find("#permission_id").val(id);
                formInputHakAkses.find("#name").val(dataInput.name);
                modalInputHakAkses.modal("show");
            })

            const formInputHakAkses = $("#formInputHakAkses");
            const submitBtn = formInputHakAkses.find("button[type=submit]");
            const modalInputHakAkses = $("#modalInputHakAkses");
            const cancelInput = () => {
                formInputHakAkses.find("permission_id").val("");
                formInputHakAkses.find("input").val("");
                formInputHakAkses.modal("hide");
            };

            formInputHakAkses.on("submit", async function(e) {
                e.preventDefault();
                const action = $(this).attr("action");
                const formData = new FormData(this);
                console.log(formData)
                const result = await sendData(action, 'POST', formData);
                if (result.status) {
                    submitBtn.attr('disabled', false).html('Simpan');
                    cancelInput();
                    table_permission.draw();
                    Swal.fire(`Berhasil disimpan`, result.message, "success");
                    modalInputHakAkses.modal("hide");
                } else {
                    submitBtn.attr('disabled', false).html('Simpan');
                    Swal.fire(`Gagal disimpan`, result.message, "error");
                }
            })

            $(document).on('hide.bs.modal', '#modalInputHakAkses', function() {
                cancelInput();
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/role-permission/index.blade.php ENDPATH**/ ?>