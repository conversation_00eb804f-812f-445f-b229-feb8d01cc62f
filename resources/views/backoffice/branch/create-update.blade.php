<x-app-layout>
    <x-slot name="header">
        {{ isset($data) ? 'Edit' : 'Tambah' }} Cabang
    </x-slot>
    <x-slot name="headerRight">
        <a href="{{ route('branch.index') }}" class="btn btn-outline-primary">
            <i class="feather-arrow-left me-2"></i>
            <span>Kembali</span>
        </a>
    </x-slot>

    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <form action="{{ route('branch.store') }}" method="POST">
                        @csrf
                        @if(isset($data))
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                           id="name" name="name"
                                           value="{{ old('name', $data->name ?? '') }}"
                                           placeholder="Masukkan nama cabang" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Kode Cabang <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('code') is-invalid @enderror"
                                           id="code" name="code"
                                           value="{{ old('code', $data->code ?? '') }}"
                                           placeholder="Masukkan kode cabang" required>
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Kode cabang harus unik</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select select2 @error('status') is-invalid @enderror"
                                            id="status" name="status" required>
                                        <option value="">Pilih Status</option>
                                        <option value="active" {{ old('status', $data->status ?? '') == 'active' ? 'selected' : '' }}>
                                            Aktif
                                        </option>
                                        <option value="inactive" {{ old('status', $data->status ?? '') == 'inactive' ? 'selected' : '' }}>
                                            Tidak Aktif
                                        </option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_off_count" class="form-label">Max Jumlah Off</label>
                                    <input type="number" class="form-control @error('max_off_count') is-invalid @enderror"
                                           id="max_off_count" name="max_off_count"
                                           value="{{ old('max_off_count', $data->max_off_count ?? 0) }}"
                                           placeholder="Masukkan maksimal jumlah off" min="0">
                                    @error('max_off_count')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Maksimal jumlah karyawan yang boleh cuti di hari yang sama</small>
                                </div>
                            </div>
                        </div>

                        <!-- Approval Levels Section -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">Approval Levels</label>
                                    <div id="approval-levels-container">
                                        @php
                                            $approvalLevels = old('approval_levels', $data->approval_levels ?? []);
                                            if (empty($approvalLevels)) {
                                                $approvalLevels = [''];
                                            }
                                        @endphp
                                        @foreach($approvalLevels as $index => $userId)
                                        <div class="approval-level-item mb-2" data-index="{{ $index }}">
                                            <div class="input-group">
                                                <span class="input-group-text">Approval {{ $index + 1 }}</span>
                                                <select class="form-select user-select" name="approval_levels[]" data-index="{{ $index }}">
                                                    <option value="">Pilih User</option>
                                                </select>
                                                <button type="button" class="btn btn-outline-danger remove-approval" {{ $index == 0 ? 'style=display:none' : '' }}>
                                                    <i class="feather-trash-2"></i>
                                                </button>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="add-approval-level">
                                        <i class="feather-plus me-1"></i>
                                        Tambah Approval Level
                                    </button>
                                    <small class="text-muted d-block mt-1">Approval akan dijalankan secara berurutan sesuai urutan yang ditentukan</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('branch.index') }}" class="btn btn-outline-secondary">
                                <i class="feather-x me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                {{ isset($data) ? 'Update' : 'Simpan' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                        <ul class="mb-0">
                            <li><strong>Nama Cabang:</strong> Nama lengkap cabang/kantor</li>
                            <li><strong>Kode Cabang:</strong> Kode unik untuk identifikasi cabang</li>
                            <li><strong>Status:</strong> Status aktif/tidak aktif cabang</li>
                        </ul>
                    </div>
                    
                    @if(isset($data))
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">Informasi Data:</h6>
                        <ul class="mb-0">
                            <li><strong>Dibuat:</strong> {{ $data->created_at->translatedFormat('d F Y H:i') }}</li>
                            @if($data->updated_at != $data->created_at)
                            <li><strong>Diupdate:</strong> {{ $data->updated_at->translatedFormat('d F Y H:i') }}</li>
                            @endif
                        </ul>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Initialize Select2
                $('.select2').select2({
                    theme: 'bootstrap-5',
                    width: '100%'
                });

                // Initialize user selects and load users
                initializeUserSelects();
                loadUsers();

                // Check initial approval levels count and hide add button if needed
                const initialCount = $('#approval-levels-container').find('.approval-level-item').length;
                if (initialCount >= 2) {
                    $('#add-approval-level').hide();
                }

                // Add approval level functionality
                $('#add-approval-level').on('click', function() {
                    addApprovalLevel();
                });

                // Remove approval level functionality
                $(document).on('click', '.remove-approval', function() {
                    $(this).closest('.approval-level-item').remove();
                    updateApprovalIndexes();

                    // Show add button if less than 2 approval levels
                    const container = $('#approval-levels-container');
                    if (container.find('.approval-level-item').length < 2) {
                        $('#add-approval-level').show();
                    }

                    // Reload users to update available options
                    loadUsers();
                });

                // Validate code uniqueness on blur
                $('#code').on('blur', function() {
                    const code = $(this).val();
                    const branchId = $('input[name="id"]').val();

                    if (code) {
                        $.ajax({
                            url: '{{ route("branch.check-code") }}',
                            method: 'POST',
                            data: {
                                code: code,
                                id: branchId,
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (!response.available) {
                                    $('#code').addClass('is-invalid');
                                    $('#code').siblings('.invalid-feedback').remove();
                                    $('#code').after('<div class="invalid-feedback">Kode cabang sudah digunakan</div>');
                                } else {
                                    $('#code').removeClass('is-invalid');
                                    $('#code').siblings('.invalid-feedback').remove();
                                }
                            }
                        });
                    }
                });

                // Form validation
                $('form').on('submit', function(e) {
                    let isValid = true;

                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'error',
                            title: 'Form Tidak Lengkap!',
                            text: 'Mohon lengkapi semua field yang wajib diisi'
                        });
                    }
                });

                // Functions for approval levels management
                function initializeUserSelects() {
                    $('.user-select').each(function() {
                        $(this).select2({
                            theme: 'bootstrap-5',
                            width: '100%',
                            placeholder: 'Pilih User'
                        });
                    });

                    // Add change event handler for user selects
                    $(document).off('change', '.user-select').on('change', '.user-select', function() {
                        // Reload all user selects to update available options
                        loadUsers();
                    });
                }

                function loadUsers() {
                    $.ajax({
                        url: '{{ route("branch.getUsers") }}',
                        method: 'GET',
                        success: function(response) {
                            updateUserSelects(response);
                        },
                        error: function() {
                            console.error('Failed to load users');
                        }
                    });
                }

                function updateUserSelects(users) {
                    const approvalLevels = @json($approvalLevels ?? []);

                    // Get currently selected users
                    const selectedUsers = [];
                    $('.user-select').each(function() {
                        const value = $(this).val();
                        if (value) {
                            selectedUsers.push(value);
                        }
                    });

                    $('.user-select').each(function(index) {
                        const select = $(this);
                        const currentValue = select.val();
                        select.empty().append('<option value="">Pilih User</option>');

                        users.forEach(function(user) {
                            // Only show user if not selected in other approval levels or is current selection
                            const isSelected = selectedUsers.includes(user.id) && user.id !== currentValue;
                            if (!isSelected) {
                                const selected = approvalLevels[index] === user.id ? 'selected' : '';
                                select.append(`<option value="${user.id}" ${selected}>${user.nama}</option>`);
                            }
                        });

                        select.trigger('change');
                    });
                }

                function addApprovalLevel() {
                    const container = $('#approval-levels-container');
                    const currentCount = container.find('.approval-level-item').length;

                    // Limit to maximum 2 approval levels
                    if (currentCount >= 2) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Batas Maksimal!',
                            text: 'Maksimal hanya 2 level approval per cabang'
                        });
                        return;
                    }

                    const newIndex = currentCount;

                    const newLevel = `
                        <div class="approval-level-item mb-2" data-index="${newIndex}">
                            <div class="input-group">
                                <span class="input-group-text">Approval ${newIndex + 1}</span>
                                <select class="form-select user-select" name="approval_levels[]" data-index="${newIndex}">
                                    <option value="">Pilih User</option>
                                </select>
                                <button type="button" class="btn btn-outline-danger remove-approval">
                                    <i class="feather-trash-2"></i>
                                </button>
                            </div>
                        </div>
                    `;

                    container.append(newLevel);

                    // Initialize select2 for new element
                    const newSelect = container.find('.approval-level-item').last().find('.user-select');
                    newSelect.select2({
                        theme: 'bootstrap-5',
                        width: '100%',
                        placeholder: 'Pilih User'
                    });

                    // Add change event handler for new select
                    newSelect.on('change', function() {
                        // Reload all user selects to update available options
                        loadUsers();
                    });

                    // Load users for all selects
                    loadUsers();
                    updateApprovalIndexes();

                    // Hide add button if reached maximum
                    if (container.find('.approval-level-item').length >= 2) {
                        $('#add-approval-level').hide();
                    }
                }

                function loadUsersForSelect(selectElement) {
                    $.ajax({
                        url: '{{ route("branch.getUsers") }}',
                        method: 'GET',
                        success: function(response) {
                            selectElement.empty().append('<option value="">Pilih User</option>');
                            response.forEach(function(user) {
                                selectElement.append(`<option value="${user.id}">${user.nama}</option>`);
                            });
                        }
                    });
                }

                function updateApprovalIndexes() {
                    $('#approval-levels-container .approval-level-item').each(function(index) {
                        $(this).attr('data-index', index);
                        $(this).find('.input-group-text').text(`Approval ${index + 1}`);
                        $(this).find('.user-select').attr('data-index', index);

                        // Show/hide remove button (first item should not have remove button)
                        const removeBtn = $(this).find('.remove-approval');
                        if (index === 0) {
                            removeBtn.hide();
                        } else {
                            removeBtn.show();
                        }
                    });
                }
            });
        </script>
    @endpush
</x-app-layout>
