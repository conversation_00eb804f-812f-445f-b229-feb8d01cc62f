<x-app-layout>
    <x-slot name="header">
        Master Data Pengaturan <PERSON>
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center flex-wrap">
                            <div class="" style="width: 300px">
                                <input type="text" class="form-control" id="search-input" placeholder="Cari nama/kode jenis absen"
                                    oninput="handleSearch(event)"
                                    style="width: 100%" />
                            </div>
                            <div class="ms-md-auto mt-md-0 mt-3">
                                @if (canPermission('Master Jenis Absen.Create'))
                                <div>
                                    <a href="{{ route('attendance-type-setting.create') }}" class="btn btn-primary">
                                        <i class="feather-plus me-2"></i>
                                        <span>Tambah Jenis A<PERSON>en</span>
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                        
                        {{-- <div class="alert alert-info mt-3">
                            <h6 class="alert-heading">Informasi Jenis Absen:</h6>
                            <ul class="mb-0">
                                <li><strong>Pengurangan Hari Cuti:</strong> Jumlah hari yang dikurangi dari jatah cuti</li>
                                <li><strong>Pengurangan Hari Libur:</strong> Jumlah hari yang dikurangi dari jatah libur</li>
                                <li><strong>Tambahan Hari:</strong> Jumlah hari tambahan yang diberikan</li>
                                <li>Contoh: Sakit = mengurangi cuti, Alpha = mengurangi cuti dan libur</li>
                            </ul>
                        </div> --}}
                        
                        <div class="table-responsive mt-4">
                            <table class="table table-hover" id="example">
                                <thead>
                                    <tr>
                                        <th style="width: 12px">No</th>
                                        <th>Nama</th>
                                        <th>Kode</th>
                                        <th>Pengurangan Cuti</th>
                                        <th>Pengurangan Libur</th>
                                        <th>Tambahan Hari</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    @include('components.alert-confirmation')

    @push('scripts')
        @include('libs.datatable')
        <script>
            let table;
            let searchTimeout;

            $(document).ready(function() {
                table = $('#example').DataTable({
                   lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('attendance-type-setting.dataTable') }}",
                        type: "POST",
                        data: function(d) {
                            d._token = "{{ csrf_token() }}";
                            d.keyword = $('#search-input').val();
                        }
                    },
                    columns: [{
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'name',
                            name: 'name'
                        },
                        {
                            data: 'code',
                            name: 'code'
                        },
                        {
                            data: 'reduction_leave_formatted',
                            name: 'reduction_days_leave'
                        },
                        {
                            data: 'reduction_off_formatted',
                            name: 'reduction_days_off'
                        },
                        {
                            data: 'additional_formatted',
                            name: 'additional_days'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                });

                $(document).on('click', '.deleteData', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");
                    const nama = dataInput.name;
                    const urlTarget = `${base_url}/attendance-type-setting/${id}`
                    await deleteDataTable(nama, urlTarget, table)
                });
            });

            function handleSearch(event) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.ajax.reload();
                }, 500);
            }

            // Add search input ID for reference
            $(document).ready(function() {
                $('input[placeholder="Cari nama/kode jenis absen"]').attr('id', 'search-input');
            });
        </script>
    @endpush
</x-app-layout>
