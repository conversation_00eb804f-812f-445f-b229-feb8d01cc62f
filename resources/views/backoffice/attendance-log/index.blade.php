<x-app-layout>
    <x-slot name="header">
        Upload Log Absensi
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="" style="width: 300px">
                            <input type="text" class="form-control" id="search-input"
                                placeholder="Cari nama, cabang, atau departemen..." oninput="handleSearch(event)"
                                style="width: 100%" />
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <input type="date" class="form-control" id="date-from" placeholder="Tanggal Dari">
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <input type="date" class="form-control" id="date-to" placeholder="Tanggal Sampai">
                        </div>
                        <div class="ms-md-auto mt-md-0 mt-3">
                            @if (canPermission('Upload Log Absensi.Create'))
                                <div class="d-flex gap-2 align-items-center">
                                    <a href="{{ route('attendance-log.downloadTemplate') }}"
                                        class="btn btn-outline-dark">
                                        <i class="feather-download me-2"></i>
                                        <span>Download Template</span>
                                    </a>
                                    <button type="button" class="btn btn-info" data-bs-toggle="modal"
                                        data-bs-target="#uploadModal">
                                        <i class="feather-upload me-2"></i>
                                        <span>Upload Excel</span>
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>

                    {{-- <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">Informasi Upload Log Absensi:</h6>
                        <ul class="mb-0">
                            <li><strong>Format Excel:</strong> Cabang, Nama, Departemen, Date Change, Time Masuk, Time Keluar</li>
                            <li><strong>Upload Berulang:</strong> Data akan menimpa berdasarkan nama + cabang + tanggal</li>
                            <li><strong>Format Tanggal:</strong> dd/mm/yyyy atau yyyy-mm-dd</li>
                            <li><strong>Format Waktu:</strong> HH.mm atau HH:mm</li>
                        </ul>
                    </div> --}}

                    <div class="d-flex align-items-center- gap-3 mt-4">
                        <button type="button" class="btn btn-outline-danger btn-sm py-3" id="deleteAll">
                            <i class="feather-trash me-1"></i>
                            <span>Hapus Semua</span>
                        </button>
                        <div class="bulk-actions" style="display: none">
                            <button type="button" class="btn btn-danger btn-sm py-3" id="deleteSelected">
                                <i class="feather-trash-2 me-1"></i>
                                <span>Hapus Terpilih</span>
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    <th style="width: 12px">No</th>
                                    <th>Cabang</th>
                                    <th>Nama</th>
                                    <th>Departemen</th>
                                    <th>Tanggal</th>
                                    <th>Jam Masuk</th>
                                    <th>Jam Keluar</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Modal -->
    @push('modals')
        <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="uploadModalLabel">Upload Log Absensi Excel</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="attendance_file" class="form-label">File Excel <span
                                        class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="attendance_file" name="attendance_file"
                                    accept=".xlsx,.xls" required>
                                <div class="form-text">Format: .xlsx atau .xls (Max: 2MB)</div>
                            </div>
                            <div class="alert alert-info">
                                <h6><i class="feather-info me-2"></i>Informasi Upload:</h6>
                                <ul class="mb-0">
                                    <li>File akan menimpa data yang sudah ada berdasarkan nama, cabang, dan tanggal</li>
                                    <li>Format kolom: Cabang, Nama, Departemen, Date Change, Time Masuk, Time Keluar</li>
                                    <li>Format tanggal: dd/mm/yyyy atau yyyy-mm-dd</li>
                                    <li>Format waktu: HH.mm.ss atau HH:mm</li>
                                </ul>
                            </div>
                            {{-- <a href="{{ route('attendance-log.downloadTemplate') }}" class="btn btn-outline-primary">
                                <i class="feather-download me-2"></i>
                                <span>Download Template</span>
                            </a> --}}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-upload me-2"></i>Upload
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endpush

    @include('components.alert-confirmation')

    @push('scripts')
        @include('libs.datatable')
        <script>
            let table;
            let searchTimeout;

            function handleSearch(event) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.draw();
                }, 500);
            }

            $(document).ready(function() {
                table = $('#example').DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('attendance-log.dataTable') }}",
                        type: "POST",
                        data: function(d) {
                            d._token = "{{ csrf_token() }}";
                            d.keyword = $('#search-input').val();
                            d.date_from = $('#date-from').val();
                            d.date_to = $('#date-to').val();
                        }
                    },
                    columns: [{
                            data: 'checkbox',
                            name: 'checkbox',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'cabang',
                            name: 'cabang'
                        },
                        {
                            data: 'nama',
                            name: 'nama'
                        },
                        {
                            data: 'departemen',
                            name: 'departemen'
                        },
                        {
                            data: 'date_formatted',
                            name: 'date_change'
                        },
                        {
                            data: 'time_masuk_formatted',
                            name: 'time_masuk'
                        },
                        {
                            data: 'time_keluar_formatted',
                            name: 'time_keluar'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ]
                });

                // Date filter functionality
                $('#date-from, #date-to').on('change', function() {
                    table.draw();
                });

                // Select All functionality
                $('#selectAll').on('change', function() {
                    const isChecked = $(this).is(':checked');
                    $('.row-checkbox').prop('checked', isChecked);
                    toggleBulkActions();
                });

                // Individual checkbox functionality
                $(document).on('change', '.row-checkbox', function() {
                    const totalCheckboxes = $('.row-checkbox').length;
                    const checkedCheckboxes = $('.row-checkbox:checked').length;

                    $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
                    toggleBulkActions();
                });

                // Toggle bulk action buttons visibility
                function toggleBulkActions() {
                    const checkedCount = $('.row-checkbox:checked').length;
                    if (checkedCount > 0) {
                        $('.bulk-actions').show();
                        $('.bulk-info').html(`<small class="text-muted">${checkedCount} data dipilih</small>`);
                    } else {
                        $('.bulk-actions').hide();
                        $('.bulk-info').html('');
                    }
                }

                // Update bulk info after table draw
                table.on('draw', function() {
                    $('#selectAll').prop('checked', false);
                    $('.bulk-actions').hide();
                    $('.bulk-info').html('');
                });

                // Upload form submission
                $('#uploadForm').on('submit', function(e) {
                    e.preventDefault();

                    var formData = new FormData(this);

                    $.ajax({
                        url: "{{ route('attendance-log.uploadExcel') }}",
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function() {
                            $('#uploadModal .btn-primary').prop('disabled', true).html(
                                '<i class="feather-loader me-2"></i>Uploading...');
                        },
                        success: function(response) {
                            if (response.status) {
                                $('#uploadModal').modal('hide');

                                // Create detailed result HTML
                                let resultHtml = `
                                    <div class="text-start">
                                        <h6>Hasil Upload:</h6>
                                        <p><strong>Total Berhasil:</strong> ${response.total_success} (${response.imported} baru, ${response.updated} update)</p>
                                        <p><strong>Total Gagal:</strong> ${response.total_failed}</p>
                                `;

                                if (response.success_rows && response.success_rows.length > 0) {
                                    resultHtml += `
                                        <div class="mt-3">
                                            <h6 class="text-success"><i class="feather-check-circle me-2"></i>Data Berhasil (${response.success_rows.length}):</h6>
                                            <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                                    `;
                                    response.success_rows.forEach(function(row) {
                                        resultHtml += `
                                            <div class="mb-2 p-2 bg-white rounded border-start border-success border-3">
                                                <strong>Baris ${row.row}:</strong> ${row.data}
                                                <span class="badge bg-success ms-2">${row.action}</span>
                                            </div>
                                        `;
                                    });
                                    resultHtml += `</div></div>`;
                                }

                                if (response.failed_rows && response.failed_rows.length > 0) {
                                    resultHtml += `
                                        <div class="mt-3">
                                            <h6 class="text-danger"><i class="feather-x-circle me-2"></i>Data Gagal (${response.failed_rows.length}):</h6>
                                            <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto; background-color: #fdf2f2;">
                                    `;
                                    response.failed_rows.forEach(function(row) {
                                        resultHtml += `
                                            <div class="mb-3 p-3 bg-white rounded border-start border-danger border-3">
                                                <div class="mb-2">
                                                    <strong class="text-dark">Baris ${row.row}:</strong>
                                                    <div class="text-muted small mt-1">${row.data}</div>
                                                </div>
                                                <div class="alert alert-danger mb-0 py-2">
                                                    <i class="feather-alert-triangle me-2"></i>
                                                    <strong>Error:</strong> ${row.error}
                                                </div>
                                            </div>
                                        `;
                                    });
                                    resultHtml += `</div></div>`;
                                }

                                resultHtml += `</div>`;

                                // Create download button for detailed results
                                let downloadButton = '';
                                if (response.failed_rows && response.failed_rows.length > 0) {
                                    downloadButton = `
                                        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="downloadErrorReport()">
                                            <i class="feather-download me-1"></i>Download Error Report
                                        </button>
                                    `;
                                }

                                Swal.fire({
                                    icon: response.total_failed > 0 ? 'warning' : 'success',
                                    title: 'Upload Selesai!',
                                    html: resultHtml,
                                    width: '800px',
                                    showConfirmButton: true,
                                    confirmButtonText: 'OK',
                                    footer: downloadButton,
                                    customClass: {
                                        popup: 'swal-wide'
                                    }
                                });

                                // Store error data for download
                                window.lastUploadErrors = response.failed_rows || [];
                                table.draw();
                                $('#uploadForm')[0].reset();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Gagal!',
                                    text: response.message
                                });
                            }
                        },
                        error: function(xhr) {
                            var message = 'Terjadi kesalahan saat upload file';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                message = xhr.responseJSON.message;
                            }
                            Swal.fire({
                                icon: 'error',
                                title: 'Upload Gagal!',
                                text: message
                            });
                        },
                        complete: function() {
                            $('#uploadModal .btn-primary').prop('disabled', false).html(
                                '<i class="feather-upload me-2"></i>Upload');
                        }
                    });
                });

                // Delete functionality
                $(document).on('click', '.deleteData', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");
                    const nama = `Log Absensi ${dataInput.nama} - ${dataInput.date_formatted}`;
                    const urlTarget = `${base_url}/attendance-log/${id}`
                    await deleteDataTable(nama, urlTarget, table)
                });

                // Bulk Delete Selected
                $('#deleteSelected').on('click', function() {
                    const selectedIds = [];
                    $('.row-checkbox:checked').each(function() {
                        selectedIds.push($(this).val());
                    });

                    if (selectedIds.length === 0) {
                        Swal.fire('Info', 'Pilih data yang ingin dihapus terlebih dahulu', 'info');
                        return;
                    }

                    Swal.fire({
                        title: 'Hapus Data Terpilih?',
                        text: `Apakah Anda yakin ingin menghapus ${selectedIds.length} data log absensi yang dipilih?`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Ya, Hapus!',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.value) {
                            $.ajax({
                                url: "{{ route('attendance-log.bulkDelete') }}",
                                type: 'POST',
                                data: {
                                    _token: "{{ csrf_token() }}",
                                    ids: selectedIds
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.draw();
                                        $('#selectAll').prop('checked', false);
                                        $('.bulk-actions').hide();
                                    } else {
                                        Swal.fire('Gagal!', response.message, 'error');
                                    }
                                },
                                error: function() {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat menghapus data', 'error'
                                    );
                                }
                            });
                        }
                    });
                });

                // Delete All
                $('#deleteAll').on('click', function() {
                    Swal.fire({
                        title: 'Hapus Semua Data?',
                        text: 'Apakah Anda yakin ingin menghapus SEMUA data log absensi? Tindakan ini tidak dapat dibatalkan!',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Ya, Hapus Semua!',
                        cancelButtonText: 'Batal',
                        input: 'text',
                        inputPlaceholder: 'Ketik "HAPUS SEMUA" untuk konfirmasi',
                        inputValidator: (value) => {
                            if (value !== 'HAPUS SEMUA') {
                                return 'Ketik "HAPUS SEMUA" untuk konfirmasi'
                            }
                        }
                    }).then((result) => {
                        if (result.value) {
                            $.ajax({
                                url: "{{ route('attendance-log.deleteAll') }}",
                                type: 'POST',
                                data: {
                                    _token: "{{ csrf_token() }}",
                                    keyword: $('#search-input').val(),
                                    date_from: $('#date-from').val(),
                                    date_to: $('#date-to').val()
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.draw();
                                        $('#selectAll').prop('checked', false);
                                        $('.bulk-actions').hide();
                                    } else {
                                        Swal.fire('Gagal!', response.message, 'error');
                                    }
                                },
                                error: function() {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat menghapus data', 'error'
                                    );
                                }
                            });
                        }
                    });
                });
            });

            // Function to download error report
            function downloadErrorReport() {
                if (!window.lastUploadErrors || window.lastUploadErrors.length === 0) {
                    Swal.fire('Info', 'Tidak ada error untuk didownload', 'info');
                    return;
                }

                let csvContent = "data:text/csv;charset=utf-8,";
                csvContent += "Baris,Data,Error\n";

                window.lastUploadErrors.forEach(function(error) {
                    csvContent += `"${error.row}","${error.data}","${error.error}"\n`;
                });

                const encodedUri = encodeURI(csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", `attendance_upload_errors_${new Date().toISOString().slice(0,10)}.csv`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        </script>

        <style>
            .swal-wide {
                max-width: 90% !important;
            }

            .swal2-popup.swal-wide {
                font-size: 14px;
            }

            .swal2-popup.swal-wide .swal2-html-container {
                max-height: 70vh;
                overflow-y: auto;
            }

            /* Bulk Actions Styling */
            .bulk-actions {
                border-left: 3px solid #dc3545;
                padding-left: 10px;
                margin-right: 15px;
            }

            .bulk-info {
                display: flex;
                align-items: center;
                min-height: 38px;
            }
        </style>
    @endpush
</x-app-layout>
