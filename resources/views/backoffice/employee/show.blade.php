<x-app-layout>
    <x-slot name="header">
        Detail Data Karyawan
    </x-slot>
    <x-slot name="headerRight">
        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
            @if (canPermission('Karyawan.Edit'))
                <a href="{{ route('employee.edit', $employee->id) }}" class="btn btn-primary">
                    <i class="feather-edit me-2"></i>
                    <span>Edit</span>
                </a>
            @endif
            <a href="{{ route('employee.index') }}" class="btn btn-outline-primary">
                <i class="feather-arrow-left me-2"></i>
                <span>Kembali</span>
            </a>
        </div>
    </x-slot>

    <div class="row">
        <div class="col-lg-12">

            <!-- Main Content Grid -->
            <div class="row">
                <!-- Biodata Section -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header py-3 bg-dark ">
                            <h5 class="mb-0 fw-bold text-white fs-16">Biodata</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="40%"><strong>Nama</strong></td>
                                    <td>: {{ $employee->nama }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Role</strong></td>
                                    <td>: {{ $employee->role ? $employee->role->name : '-' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Join Date</strong></td>
                                    <td>: {{ $employee->formatted_join_date }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Kode</strong></td>
                                    <td>: {{ $employee->kode ?: '-' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Source</strong></td>
                                    <td>: {{ $employee->source ?: '-' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Gender</strong></td>
                                    <td>: {{ $employee->gender_label }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Tanggal Lahir</strong></td>
                                    <td>: {{ $employee->formatted_tanggal_lahir }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Nama Asli KTP</strong></td>
                                    <td>: {{ $employee->nama_asli_ktp ?: '-' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Hari Off</strong></td>
                                    <td>: {{ $employee->hari_off_formatted }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status</strong></td>
                                    <td>: {!! $employee->status_badge !!}</td>
                                </tr>
                                <tr>
                                    <td><strong>Resign Date</strong></td>
                                    <td>: {{ $employee->formatted_resign_date }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header py-3" style="background-color: #4a90a4; color: white;">
                            <h5 class="mb-0 fw-bold text-white fs-16">Data Lain</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @if ($employee->customFields && $employee->customFields->count() > 0)
                                    @foreach ($employee->customFields as $customField)
                                        <div class="col-md-6 mb-3">
                                            <strong>{{ $customField->field_name }}:</strong><br>
                                            <div class="border p-2 rounded bg-light">
                                                {{ $customField->field_value ?: '-' }}
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    @php
                                        $hasStaticFields = false;
                                        for ($i = 1; $i <= 8; $i++) {
                                            $field = 'custom_field_' . $i;
                                            if ($employee->$field) {
                                                $hasStaticFields = true;
                                                break;
                                            }
                                        }
                                    @endphp

                                    @if ($hasStaticFields)
                                        @for ($i = 1; $i <= 8; $i++)
                                            @php $field = 'custom_field_' . $i; @endphp
                                            @if ($employee->$field)
                                                <div class="col-md-6 mb-3">
                                                    <strong>Custom Field {{ $i }}:</strong><br>
                                                    <div class="border p-2 rounded bg-light">
                                                        {{ $employee->$field }}
                                                    </div>
                                                </div>
                                            @endif
                                        @endfor
                                    @else
                                        <div class="col-12">
                                            <p class="text-muted text-center">Tidak ada data custom field</p>
                                        </div>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <!-- Data Rekening Section -->
                    @if (canPermission('Karyawan.View Rekening'))
                        <div class="col-lg-12 mb-4">
                            <div class="card h-100">
                                <div class="card-header py-3" style="background-color: #4a90a4;">
                                    <h5 class="mb-0 fw-bold text-white fs-16">Data Rekening</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="40%"><strong>Nama Rekening</strong></td>
                                            <td>: {{ $employee->nama_rekening ?: '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>No Rekening</strong></td>
                                            <td>: {{ $employee->no_rekening ?: '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Bank</strong></td>
                                            <td>: {{ $employee->bank ?: '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif
                    <!-- Data Berjalan Section -->
                    <div class="col-lg-12 mb-4">
                        <div class="card h-100">
                            <div class="card-header py-3" style="background-color: #4a90a4;">
                                <h5 class="mb-0 fw-bold text-white fs-16">Data Berjalan</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="50%"><strong>Total Cuti Tahunan (Tahun Ini)</strong></td>
                                        <td>: {{ $employee->getJumlahCutiTahunanDinamis() }} hari</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2">
                                            <strong>Detail Hak Cuti:</strong>
                                            @php
                                                $leaveSummary = $employee->getLeaveEntitlementsSummary();
                                            @endphp
                                            @if (count($leaveSummary) > 0)
                                                <div class="mt-2">
                                                    @foreach ($leaveSummary as $leave)
                                                        <div
                                                            class="d-flex justify-content-between align-items-center mb-1">
                                                            <span class="text-muted">{{ $leave['type'] }}:</span>
                                                            <span class="badge bg-info">
                                                                {{ $leave['allocated'] }} hari
                                                                (Sisa: {{ $leave['remaining'] }})
                                                            </span>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @else
                                                <div class="mt-2">
                                                    <small class="text-muted">Belum ada alokasi cuti untuk tahun
                                                        ini</small>
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Masa Kerja</strong></td>
                                        <td>: {{ $employee->masa_kerja_formatted }} ({{ $employee->masa_kerja_hari }}
                                            hari)</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Data Perlengkapan Section -->
                    <div class="col-lg-12 mb-4">
                        <div class="card h-100">
                            <div class="card-header py-3" style="background-color: #4a90a4; color: white;">
                                <h5 class="mb-0 fw-bold text-white fs-16">Data Perlengkapan</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="40%"><strong>Ukuran Seragam</strong></td>
                                        <td>: {{ $employee->ukuran_seragam ?: '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ukuran Sendal</strong></td>
                                        <td>: {{ $employee->ukuran_sendal ?: '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ukuran Kaos</strong></td>
                                        <td>: {{ $employee->ukuran_kaos ?: '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Jumlah Seragam</strong></td>
                                        <td>: {{ $employee->jumlah_seragam ?: '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Nomer Loker</strong></td>
                                        <td>: {{ $employee->nomer_loker ?: '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Kendaraan</strong></td>
                                        <td>: {{ $employee->kendaraan ?: '-' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Data Deposit Section -->
                    <div class="col-lg-12 mb-4">
                        <div class="card">
                            <div class="card-header py-3 bg-dark text-white">
                                <h5 class="mb-0 fw-bold text-white fs-16">Data Deposit</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Deposit Seragam:</strong><br>
                                        {{ $employee->deposit_seragam ? 'Rp ' . number_format($employee->deposit_seragam, 0, ',', '.') : '-' }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Tanggal Deposit:</strong><br>
                                        {{ $employee->tanggal_deposit ? $employee->tanggal_deposit->format('d/m/Y') : '-' }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Pengembalian Seragam:</strong><br>
                                        {{ $employee->pengembalian_seragam ? 'Rp ' . number_format($employee->pengembalian_seragam, 0, ',', '.') : '-' }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Tanggal Pengembalian Deposit:</strong><br>
                                        {{ $employee->tanggal_pengembalian_deposit ? $employee->tanggal_pengembalian_deposit->format('d/m/Y') : '-' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
