<x-app-layout>
    <x-slot name="header">
        {{ isset($data) ? 'Edit' : 'Tambah' }} Data <PERSON><PERSON>wan
    </x-slot>
    <x-slot name="headerRight">
        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
            <a href="{{ route('employee.index') }}" class="btn btn-outline-primary">
                <i class="feather-arrow-left me-2"></i>
                <span>Kembali</span>
            </a>
        </div>
    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <x-validation-errors />
                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                    <form class="needs-validation" novalidate action="{{ route('employee.store') }}" id="formSubmit"
                        method="POST">
                        @csrf
                        @if ($data ?? false)
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif

                        <!-- Biodata Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3 bg-dark">
                                <h5 class="mb-0 text-white fw-bold fs-16">Biodata</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <label class="form-label">Nama <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="nama" placeholder="Nama"
                                            required value="{{ old('nama', $data->nama ?? null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Role</label>
                                        @php
                                            $roles = App\Models\Role::pluck('name', 'id');
                                        @endphp
                                        <select class="form-control select2" name="role_id"
                                            data-select2-selector="icon">
                                            <option value="">Pilih Role</option>
                                            @foreach ($roles as $key => $value)
                                                <option value="{{ $key }}"
                                                    {{ old('role_id', $data->role_id ?? null) == $key ? 'selected' : '' }}>
                                                    {{ $value }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Join Date</label>
                                        <input type="date" class="form-control" name="join_date"
                                            value="{{ old('join_date', $data->join_date ?? null ? $data->join_date->format('Y-m-d') : null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Kode <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('kode') is-invalid @enderror"
                                            name="kode" placeholder="Kode Karyawan"
                                            value="{{ old('kode', $data->kode ?? null) }}" required>
                                        @error('kode')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Source</label>
                                        <input type="text" class="form-control" name="source" placeholder="Source"
                                            value="{{ old('source', $data->source ?? null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Gender</label>
                                        <select class="form-control select2" name="gender">
                                            <option value="">Pilih Gender</option>
                                            <option value="male"
                                                {{ old('gender', $data->gender ?? null) == 'male' ? 'selected' : '' }}>
                                                Laki-laki</option>
                                            <option value="female"
                                                {{ old('gender', $data->gender ?? null) == 'female' ? 'selected' : '' }}>
                                                Perempuan</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Tanggal Lahir</label>
                                        <input type="date" class="form-control" name="tanggal_lahir"
                                            value="{{ old('tanggal_lahir', $data->tanggal_lahir ?? null ? $data->tanggal_lahir->format('Y-m-d') : null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Nama Asli KTP</label>
                                        <input type="text" class="form-control" name="nama_asli_ktp"
                                            placeholder="Nama Asli KTP"
                                            value="{{ old('nama_asli_ktp', $data->nama_asli_ktp ?? null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Hari Off (Optional)</label>
                                        @php
                                            $days = [
                                                'monday' => 'Senin',
                                                'tuesday' => 'Selasa',
                                                'wednesday' => 'Rabu',
                                                'thursday' => 'Kamis',
                                                'friday' => 'Jumat',
                                                'saturday' => 'Sabtu',
                                                'sunday' => 'Minggu',
                                            ];
                                            $selected_days = old('hari_off', $data->hari_off ?? []);
                                        @endphp
                                        <select class="form-control select2" name="hari_off[]" multiple>
                                            @foreach ($days as $key => $value)
                                                <option value="{{ $key }}"
                                                    {{ in_array($key, $selected_days) ? 'selected' : '' }}>
                                                    {{ $value }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-control select2" name="status" required>
                                            <option value="">Pilih Status</option>
                                            <option value="active"
                                                {{ old('status', $data->status ?? 'active') == 'active' ? 'selected' : '' }}>
                                                Aktif</option>
                                            <option value="inactive"
                                                {{ old('status', $data->status ?? null) == 'inactive' ? 'selected' : '' }}>
                                                Tidak Aktif</option>
                                            <option value="resigned"
                                                {{ old('status', $data->status ?? null) == 'resigned' ? 'selected' : '' }}>
                                                Resign</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Resign Date</label>
                                        <input type="date" class="form-control" name="resign_date"
                                            value="{{ old('resign_date', $data->resign_date ?? null ? $data->resign_date->format('Y-m-d') : null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Cabang</label>
                                        @php
                                            $branches = App\Models\Branch::active()->pluck('name', 'id');
                                        @endphp
                                        <select class="form-control select2" name="branch_id">
                                            <option value="">Pilih Cabang</option>
                                            @foreach ($branches as $key => $value)
                                                <option value="{{ $key }}"
                                                    {{ old('branch_id', $data->branch_id ?? null) == $key ? 'selected' : '' }}>
                                                    {{ $value }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Rekening Section -->
                        @if (canPermission('Karyawan.Input Rekening'))
                            <div class="card mb-4">
                                <div class="card-header py-3" style="background-color: #4a90a4; ">
                                    <h5 class="mb-0 fw-bold fs-16 text-white">Data Rekening</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-4">
                                        <div class="col-md-4">
                                            <label class="form-label">Nama Rekening</label>
                                            <input type="text" class="form-control" name="nama_rekening"
                                                placeholder="Nama Rekening"
                                                value="{{ old('nama_rekening', $data->nama_rekening ?? null) }}">
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">No Rekening</label>
                                            <input type="text" class="form-control" name="no_rekening"
                                                placeholder="No Rekening"
                                                value="{{ old('no_rekening', $data->no_rekening ?? null) }}">
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Bank</label>
                                            <input type="text" class="form-control" name="bank"
                                                placeholder="Bank" value="{{ old('bank', $data->bank ?? null) }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        <!-- Data Perlengkapan Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3" style="background-color: #4a90a4;">
                                <h5 class="mb-0 fw-bold fs-16 text-white">Data Perlengkapan</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <label class="form-label">Ukuran Seragam</label>
                                        <input type="text" class="form-control" name="ukuran_seragam"
                                            placeholder="Ukuran Seragam"
                                            value="{{ old('ukuran_seragam', $data->ukuran_seragam ?? null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Ukuran Sendal</label>
                                        <input type="text" class="form-control" name="ukuran_sendal"
                                            placeholder="Ukuran Sendal"
                                            value="{{ old('ukuran_sendal', $data->ukuran_sendal ?? null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Ukuran Kaos</label>
                                        <input type="text" class="form-control" name="ukuran_kaos"
                                            placeholder="Ukuran Kaos"
                                            value="{{ old('ukuran_kaos', $data->ukuran_kaos ?? null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Jumlah Seragam</label>
                                        <input type="number" class="form-control" name="jumlah_seragam"
                                            placeholder="Jumlah Seragam" min="0"
                                            value="{{ old('jumlah_seragam', $data->jumlah_seragam ?? null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Nomer Loker</label>
                                        <input type="text" class="form-control" name="nomer_loker"
                                            placeholder="Nomer Loker"
                                            value="{{ old('nomer_loker', $data->nomer_loker ?? null) }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Kendaraan</label>
                                        <input type="text" class="form-control" name="kendaraan"
                                            placeholder="Kendaraan"
                                            value="{{ old('kendaraan', $data->kendaraan ?? null) }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Deposit Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3 bg-dark">
                                <h5 class="mb-0 fw-bold fs-16 text-white">Data Deposit</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-3">
                                        <label class="form-label">Deposit Seragam</label>
                                        <input type="number" class="form-control" name="deposit_seragam"
                                            placeholder="Deposit Seragam" step="0.01" min="0"
                                            value="{{ old('deposit_seragam', $data->deposit_seragam ?? null) }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Tanggal Deposit</label>
                                        <input type="date" class="form-control" name="tanggal_deposit"
                                            value="{{ old('tanggal_deposit', $data->tanggal_deposit ?? null ? $data->tanggal_deposit->format('Y-m-d') : null) }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Pengembalian Seragam</label>
                                        <input type="number" class="form-control" name="pengembalian_seragam"
                                            placeholder="Pengembalian Seragam" step="0.01" min="0"
                                            value="{{ old('pengembalian_seragam', $data->pengembalian_seragam ?? null) }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Tanggal Pengembalian Deposit</label>
                                        <input type="date" class="form-control"
                                            name="tanggal_pengembalian_deposit"
                                            value="{{ old('tanggal_pengembalian_deposit', $data->tanggal_pengembalian_deposit ?? null ? $data->tanggal_pengembalian_deposit->format('Y-m-d') : null) }}">
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Custom Fields Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3" style="background-color: #4a90a4;">
                                <h5 class="mb-0 fw-bold fs-16 text-white">Data Lain</h5>
                                <button type="button" class="btn btn-sm btn-light float-end" id="addCustomField">
                                    <i class="feather-plus me-1"></i> Tambah Field
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="customFieldsContainer">
                                    @php
                                        $existingCustomFields = [];
                                        $hasCustomFields = false;

                                        // Check for dynamic custom fields first
                                        if (isset($data) && $data->customFields && $data->customFields->count() > 0) {
                                            foreach ($data->customFields as $field) {
                                                $existingCustomFields[] = [
                                                    'name' => $field->field_name,
                                                    'value' => $field->field_value,
                                                ];
                                            }
                                            $hasCustomFields = true;
                                        }

                                        // Fallback to old static fields if no dynamic fields exist
                                        if (!$hasCustomFields && isset($data)) {
                                            for ($i = 1; $i <= 8; $i++) {
                                                $fieldName = 'custom_field_' . $i;
                                                if (isset($data->$fieldName) && !empty($data->$fieldName)) {
                                                    $existingCustomFields[] = [
                                                        'name' => 'Custom Field ' . $i,
                                                        'value' => $data->$fieldName,
                                                    ];
                                                    $hasCustomFields = true;
                                                }
                                            }
                                        }

                                        // If no custom fields exist, create one empty field
                                        if (!$hasCustomFields) {
                                            $existingCustomFields[] = [
                                                'name' => '',
                                                'value' => '',
                                            ];
                                        }
                                    @endphp

                                    @foreach ($existingCustomFields as $index => $field)
                                        <div class="row g-3 mb-3 custom-field-row">
                                            <div class="col-md-4">
                                                <input type="text" class="form-control"
                                                    name="custom_field_names[]" placeholder="Nama Field"
                                                    value="{{ old('custom_field_names.' . $index, $field['name']) }}">
                                            </div>
                                            <div class="col-md-7">
                                                <textarea class="form-control" name="custom_field_values[]" rows="2" placeholder="Nilai Field">{{ old('custom_field_values.' . $index, $field['value']) }}</textarea>
                                            </div>
                                            <div class="col-md-1">
                                                <button type="button"
                                                    class="btn btn-danger btn-sm remove-custom-field">
                                                    <i class="feather-trash-2"></i>
                                                </button>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <a href="{{ route('employee.index') }}" class="btn btn-outline-secondary">
                                <span>Batal</span>
                            </a>
                            <button type="button" class="btn btn-primary btnSubmit">
                                <span>Simpan</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Initialize Select2
                $('.select2').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    placeholder: function() {
                        return $(this).data('placeholder') || 'Pilih...';
                    },
                    allowClear: true
                });

                // Initialize Select2 for multiple hari off
                $('select[name="hari_off[]"]').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    placeholder: 'Pilih hari off (optional)',
                    allowClear: true,
                    closeOnSelect: false
                });

                // Form submission handler
                $('.btnSubmit').on('click', function() {
                    // Basic validation
                    const nama = $('input[name="nama"]').val();
                    const status = $('select[name="status"]').val();

                    if (!nama.trim()) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Nama Diperlukan!',
                            text: 'Silakan masukkan nama karyawan.'
                        });
                        return false;
                    }

                    if (!status) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Status Diperlukan!',
                            text: 'Silakan pilih status karyawan.'
                        });
                        return false;
                    }

                    $('#formSubmit').submit();
                });

                // Validate kode uniqueness on blur
                $('input[name="kode"]').on('blur', function() {
                    const kode = $(this).val();
                    const employeeId = $('input[name="id"]').val();

                    if (kode) {
                        $.ajax({
                            url: '{{ route('employee.check-kode') }}',
                            method: 'POST',
                            data: {
                                kode: kode,
                                id: employeeId,
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (!response.available) {
                                    $('input[name="kode"]').addClass('is-invalid');
                                    $('input[name="kode"]').after(
                                        '<div class="invalid-feedback">Kode karyawan sudah digunakan</div>'
                                        );
                                } else {
                                    $('input[name="kode"]').removeClass('is-invalid');
                                    $('input[name="kode"]').siblings('.invalid-feedback').remove();
                                }
                            }
                        });
                    }
                });

                // Custom Fields Management
                $('#addCustomField').on('click', function() {
                    const newField = `
                        <div class="row g-3 mb-3 custom-field-row">
                            <div class="col-md-4">
                                <input type="text" class="form-control" name="custom_field_names[]" placeholder="Nama Field">
                            </div>
                            <div class="col-md-7">
                                <textarea class="form-control" name="custom_field_values[]" rows="2" placeholder="Nilai Field"></textarea>
                            </div>
                            <div class="col-md-1">
                                <button type="button" class="btn btn-danger btn-sm remove-custom-field">
                                    <i class="feather-trash-2"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    $('#customFieldsContainer').append(newField);
                });

                // Remove custom field
                $(document).on('click', '.remove-custom-field', function() {
                    const customFieldRows = $('.custom-field-row');

                    if (customFieldRows.length > 1) {
                        $(this).closest('.custom-field-row').remove();
                    } else {
                        // If this is the last field, just clear its content instead of removing
                        const row = $(this).closest('.custom-field-row');
                        row.find('input[name="custom_field_names[]"]').val('');
                        row.find('textarea[name="custom_field_values[]"]').val('');

                        Swal.fire({
                            icon: 'info',
                            title: 'Info',
                            text: 'Field terakhir telah dikosongkan. Minimal harus ada satu custom field.',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                });
            });
        </script>
    @endpush
</x-app-layout>
