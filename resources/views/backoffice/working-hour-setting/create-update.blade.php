<x-app-layout>
    <x-slot name="header">
        {{ isset($data) ? 'Edit' : 'Tambah' }} Pengaturan Jam Kerja
    </x-slot>
    <x-slot name="headerRight">
        <a href="{{ route('working-hour-setting.index') }}" class="btn btn-outline-primary">
            <i class="feather-arrow-left me-2"></i>
            <span>Kembali</span>
        </a>
    </x-slot>

    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <form action="{{ route('working-hour-setting.store') }}" method="POST">
                        @csrf
                        @if(isset($data))
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_id" class="form-label">Role <span class="text-danger">*</span></label>
                                    <select class="form-select select2 @error('role_id') is-invalid @enderror"
                                            id="role_id" name="role_id" required>
                                        <option value="">Pilih Role</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role->id }}" {{ old('role_id', $data->role_id ?? '') == $role->id ? 'selected' : '' }}>
                                                {{ $role->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('role_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Satu role dapat memiliki beberapa pengaturan jam kerja</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="working_hour_type" class="form-label">Jenis Jam Kerja <span class="text-danger">*</span></label>
                                    <select class="form-select select2 @error('working_hour_type') is-invalid @enderror"
                                            id="working_hour_type" name="working_hour_type" required>
                                        <option value="">Pilih Jenis Jam Kerja</option>
                                        @foreach($workingHourTypes as $key => $value)
                                            <option value="{{ $key }}" {{ old('working_hour_type', $data->working_hour_type ?? '') == $key ? 'selected' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('working_hour_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">Jam Mulai</label>
                                    <input type="time" class="form-control @error('start_time') is-invalid @enderror" 
                                           id="start_time" name="start_time" 
                                           value="{{ old('start_time', isset($data) && $data->start_time ? date('H:i', strtotime($data->start_time)) : '') }}">
                                    @error('start_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">Jam Selesai</label>
                                    <input type="time" class="form-control @error('end_time') is-invalid @enderror" 
                                           id="end_time" name="end_time" 
                                           value="{{ old('end_time', isset($data) && $data->end_time ? date('H:i', strtotime($data->end_time)) : '') }}">
                                    @error('end_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input @error('is_need_setting_day') is-invalid @enderror" 
                                               type="checkbox" id="is_need_setting_day" name="is_need_setting_day" value="1"
                                               {{ old('is_need_setting_day', $data->is_need_setting_day ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_need_setting_day">
                                            Perlu Pengaturan Hari Kerja Khusus
                                        </label>
                                        @error('is_need_setting_day')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">
                                        Centang jika role ini memerlukan pengaturan hari kerja yang berbeda dari standar
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('working-hour-setting.index') }}" class="btn btn-outline-secondary">
                                <i class="feather-x me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                {{ isset($data) ? 'Update' : 'Simpan' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                        <ul class="mb-0">
                            <li><strong>Role:</strong> Pilih role yang akan diatur jam kerjanya</li>
                            <li><strong>Jenis Jam Kerja:</strong> Pilih tipe shift atau jam kerja</li>
                            <li><strong>Jam Mulai/Selesai:</strong> Tentukan waktu mulai dan selesai kerja</li>
                            <li><strong>Pengaturan Hari:</strong> Centang jika perlu atur hari kerja khusus</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Catatan:</h6>
                        <ul class="mb-0">
                            <li>Setiap role hanya bisa memiliki satu pengaturan jam kerja</li>
                            <li>Jam selesai harus lebih besar dari jam mulai</li>
                            <li>Untuk shift malam, gunakan format 24 jam</li>
                        </ul>
                    </div>
                    
                    @if(isset($data))
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">Informasi Data:</h6>
                        <ul class="mb-0">
                            <li><strong>Dibuat:</strong> {{ $data->created_at->translatedFormat('d F Y H:i') }}</li>
                            @if($data->updated_at != $data->created_at)
                            <li><strong>Diupdate:</strong> {{ $data->updated_at->translatedFormat('d F Y H:i') }}</li>
                            @endif
                        </ul>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Form validation
                $('form').on('submit', function(e) {
                    let isValid = true;
                    
                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });
                    
                    // Validate time range
                    let startTime = $('#start_time').val();
                    let endTime = $('#end_time').val();
                    
                    if (startTime && endTime && startTime >= endTime) {
                        isValid = false;
                        $('#end_time').addClass('is-invalid');
                        Swal.fire({
                            icon: 'error',
                            title: 'Waktu Tidak Valid!',
                            text: 'Jam selesai harus lebih besar dari jam mulai'
                        });
                    }
                    
                    if (!isValid) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'error',
                            title: 'Form Tidak Lengkap!',
                            text: 'Mohon lengkapi semua field yang wajib diisi'
                        });
                    }
                });

                // Clear validation on input change
                $('input, select').on('change', function() {
                    $(this).removeClass('is-invalid');
                });
            });
        </script>
    @endpush
</x-app-layout>
