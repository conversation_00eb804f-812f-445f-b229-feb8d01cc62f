<x-app-layout>
    <x-slot name="header">
        Pengaturan Akses Cabang Pengguna
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="" style="width: 300px">
                            <input type="text" class="form-control" placeholder="Cari nama pengguna/email"
                                oninput="handleSearch(event)" style="width: 100%" />
                        </div>
                        <div class="ms-md-auto mt-md-0 mt-3">
                            {{-- @if (canPermission('User Access Branch.Create')) --}}
                            <div>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                    data-bs-target="#accessModal">
                                    <i class="feather-plus me-2"></i>
                                    <span>Atur Aks<PERSON></span>
                                </button>
                            </div>
                            {{-- @endif --}}
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">Informasi Akses Cabang:</h6>
                        <ul class="mb-0">
                            <li>Pengguna hanya dapat mengakses data dari cabang yang telah diberikan akses</li>
                            <li>Jika tidak ada akses cabang, pengguna tidak dapat mengakses data cabang manapun</li>
                            <li>Admin dapat mengatur akses cabang untuk setiap pengguna</li>
                        </ul>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Nama Pengguna</th>
                                    <th>Email</th>
                                    <th>Cabang yang Dapat Diakses</th>
                                    <th>Jumlah Cabang</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Access Modal -->
    @push('modals')
        <div class="modal fade" id="accessModal" tabindex="-1" aria-labelledby="accessModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-center">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="accessModalLabel">Atur Akses Cabang</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="accessForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="user_id" class="form-label">Pilih Pengguna <span
                                        class="text-danger">*</span></label>
                                <select class="form-select select2" id="user_id" name="user_id" required>
                                    <option value="">Pilih Pengguna</option>
                                    @foreach ($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->nama }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Pilih Cabang yang Dapat Diakses <span
                                        class="text-danger">*</span></label>
                                <div class="row" id="branchCheckboxes">
                                    @foreach ($branches as $branch)
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="branch_ids[]"
                                                    value="{{ $branch->id }}" id="branch_{{ $branch->id }}">
                                                <label class="form-check-label" for="branch_{{ $branch->id }}">
                                                    {{ $branch->name }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll">
                                        <strong>Pilih Semua Cabang</strong>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="feather-x me-2"></i>
                                Batal
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                Simpan Akses
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endpush

    @include('components.alert-confirmation')

    @push('scripts')
        @include('libs.datatable')
        <script>
            let table;
            let searchTimeout;

            $(document).ready(function() {
                table = $('#example').DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('user-access-branch.dataTable') }}",
                        type: "POST",
                        data: function(d) {
                            d._token = "{{ csrf_token() }}";
                            d.keyword = $('#search-input').val();
                        }
                    },
                    columns: [{
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'nama',
                            name: 'nama'
                        },
                        {
                            data: 'email',
                            name: 'email'
                        },
                        {
                            data: 'branches',
                            name: 'branches',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'branch_count',
                            name: 'branch_count',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ]
                });

                // Select all checkbox functionality
                $('#selectAll').on('change', function() {
                    $('input[name="branch_ids[]"]').prop('checked', $(this).is(':checked'));
                });

                // Update select all when individual checkboxes change
                $('input[name="branch_ids[]"]').on('change', function() {
                    let totalCheckboxes = $('input[name="branch_ids[]"]').length;
                    let checkedCheckboxes = $('input[name="branch_ids[]"]:checked').length;
                    $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
                });

                // Load user access when user is selected
                $('#user_id').on('change', function() {
                    let userId = $(this).val();
                    if (userId) {
                        loadUserAccess(userId);
                    } else {
                        $('input[name="branch_ids[]"]').prop('checked', false);
                        $('#selectAll').prop('checked', false);
                    }
                });

                // Form submission
                $('#accessForm').on('submit', function(e) {
                    e.preventDefault();

                    let formData = new FormData(this);

                    $.ajax({
                        url: "{{ route('user-access-branch.store') }}",
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        headers: {
                            'X-CSRF-TOKEN': "{{ csrf_token() }}"
                        },
                        success: function(response) {
                            if (response.status) {
                                $('#accessModal').modal('hide');
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Berhasil!',
                                    text: response.message,
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                table.ajax.reload();
                                $('#accessForm')[0].reset();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Gagal!',
                                    text: response.message
                                });
                            }
                        },
                        error: function(xhr) {
                            let response = xhr.responseJSON;
                            Swal.fire({
                                icon: 'error',
                                title: 'Terjadi Kesalahan!',
                                text: response.message || 'Terjadi kesalahan'
                            });
                        }
                    });
                });

                // Edit access functionality
                $(document).on('click', '.editAccess', function() {
                    let userId = $(this).data('user-id');
                    let userName = $(this).data('user-name');

                    $('#user_id').val(userId);
                    $('#accessModalLabel').text(`Edit Akses Cabang - ${userName}`);
                    loadUserAccess(userId);
                    $('#accessModal').modal('show');
                });

                // Remove all access functionality
                $(document).on('click', '.removeAllAccess', function() {
                    let userId = $(this).data('user-id');
                    let userName = $(this).data('user-name');

                    $('#confirmationModal').modal('show');
                    $('#confirmationModal .modal-body p').text(
                        `Apakah Anda yakin ingin menghapus semua akses cabang untuk "${userName}"?`);

                    $('#confirmDelete').off('click').on('click', function() {
                        $.ajax({
                            url: "{{ route('user-access-branch.store') }}",
                            type: 'POST',
                            data: {
                                _token: "{{ csrf_token() }}",
                                user_id: userId,
                                branch_ids: []
                            },
                            success: function(response) {
                                $('#confirmationModal').modal('hide');
                                if (response.status) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Berhasil!',
                                        text: 'Semua akses cabang berhasil dihapus',
                                        timer: 2000,
                                        showConfirmButton: false
                                    });
                                    table.ajax.reload();
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Gagal!',
                                        text: response.message
                                    });
                                }
                            },
                            error: function(xhr) {
                                $('#confirmationModal').modal('hide');
                                let response = xhr.responseJSON;
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Terjadi Kesalahan!',
                                    text: response.message || 'Terjadi kesalahan'
                                });
                            }
                        });
                    });
                });
            });

            function loadUserAccess(userId) {
                $.ajax({
                    url: "{{ route('user-access-branch.getUserAccess') }}",
                    type: 'GET',
                    data: {
                        user_id: userId
                    },
                    success: function(response) {
                        $('input[name="branch_ids[]"]').prop('checked', false);
                        response.forEach(function(branchId) {
                            $(`#branch_${branchId}`).prop('checked', true);
                        });

                        // Update select all checkbox
                        let totalCheckboxes = $('input[name="branch_ids[]"]').length;
                        let checkedCheckboxes = $('input[name="branch_ids[]"]:checked').length;
                        $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal Memuat Data!',
                            text: 'Gagal memuat data akses pengguna'
                        });
                    }
                });
            }

            function handleSearch(event) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.ajax.reload();
                }, 500);
            }

            // Add search input ID for reference
            $(document).ready(function() {
                $('input[placeholder="Cari nama pengguna/email"]').attr('id', 'search-input');
            });
        </script>
    @endpush
</x-app-layout>
