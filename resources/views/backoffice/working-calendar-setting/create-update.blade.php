<x-app-layout>
    <x-slot name="header">
        {{ isset($data) ? 'Edit' : 'Tambah' }} <PERSON><PERSON><PERSON>
    </x-slot>
    <x-slot name="headerRight">
        <a href="{{ route('working-calendar-setting.index') }}" class="btn btn-outline-primary">
            <i class="feather-arrow-left me-2"></i>
            <span>Kembali</span>
        </a>
    </x-slot>

    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <form action="{{ route('working-calendar-setting.store') }}" method="POST">
                        @csrf
                        @if(isset($data))
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date" class="form-label">Tanggal <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('date') is-invalid @enderror" 
                                           id="date" name="date" 
                                           value="{{ old('date', isset($data) && $data->date ? $data->date->format('Y-m-d') : '') }}" 
                                           required>
                                    @error('date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="day_name" class="form-label">Nama Hari</label>
                                    <input type="text" class="form-control @error('day_name') is-invalid @enderror" 
                                           id="day_name" name="day_name" 
                                           value="{{ old('day_name', $data->day_name ?? '') }}" 
                                           placeholder="Akan otomatis terisi berdasarkan tanggal" readonly>
                                    @error('day_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="week_number" class="form-label">Minggu Ke</label>
                                    <input type="number" class="form-control @error('week_number') is-invalid @enderror" 
                                           id="week_number" name="week_number" 
                                           value="{{ old('week_number', $data->week_number ?? '') }}" 
                                           placeholder="Akan otomatis terisi" min="1" max="53" readonly>
                                    @error('week_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="year" class="form-label">Tahun</label>
                                    <input type="number" class="form-control @error('year') is-invalid @enderror" 
                                           id="year" name="year" 
                                           value="{{ old('year', $data->year ?? '') }}" 
                                           placeholder="Akan otomatis terisi" min="2020" max="2050" readonly>
                                    @error('year')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="month" class="form-label">Bulan</label>
                                    <input type="number" class="form-control @error('month') is-invalid @enderror" 
                                           id="month" name="month" 
                                           value="{{ old('month', $data->month ?? '') }}" 
                                           placeholder="Akan otomatis terisi" min="1" max="12" readonly>
                                    @error('month')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="day_type" class="form-label">Jenis Hari <span class="text-danger">*</span></label>
                                    <select class="form-select select2 @error('day_type') is-invalid @enderror"
                                            id="day_type" name="day_type" required>
                                        <option value="">Pilih Jenis Hari</option>
                                        @foreach($dayTypes as $key => $value)
                                            <option value="{{ $key }}" {{ old('day_type', $data->day_type ?? '') == $key ? 'selected' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('day_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status Libur</label>
                                    <div class="form-check mt-2">
                                        <input class="form-check-input @error('is_holiday') is-invalid @enderror" 
                                               type="checkbox" id="is_holiday" name="is_holiday" value="1"
                                               {{ old('is_holiday', $data->is_holiday ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_holiday">
                                            Hari Libur/Hari Raya
                                        </label>
                                        @error('is_holiday')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">
                                        Centang jika tanggal ini adalah hari libur nasional atau hari raya
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('working-calendar-setting.index') }}" class="btn btn-outline-secondary">
                                <i class="feather-x me-2"></i>
                                Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather-save me-2"></i>
                                {{ isset($data) ? 'Update' : 'Simpan' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Petunjuk Pengisian:</h6>
                        <ul class="mb-0">
                            <li><strong>Tanggal:</strong> Pilih tanggal yang akan diatur</li>
                            <li><strong>Jenis Hari:</strong> Weekdays (hari kerja) atau Weekend (akhir pekan)</li>
                            <li><strong>Hari Libur:</strong> Centang jika tanggal tersebut adalah hari libur</li>
                            <li>Field lain akan otomatis terisi berdasarkan tanggal</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Catatan:</h6>
                        <ul class="mb-0">
                            <li>Setiap tanggal hanya bisa diinput sekali</li>
                            <li>Gunakan fitur "Generate Kalender" untuk membuat kalender tahunan</li>
                            <li>Hari libur akan mempengaruhi perhitungan hari kerja</li>
                        </ul>
                    </div>
                    
                    @if(isset($data))
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">Informasi Data:</h6>
                        <ul class="mb-0">
                            <li><strong>Dibuat:</strong> {{ $data->created_at->translatedFormat('d F Y H:i') }}</li>
                            @if($data->updated_at != $data->created_at)
                            <li><strong>Diupdate:</strong> {{ $data->updated_at->translatedFormat('d F Y H:i') }}</li>
                            @endif
                        </ul>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Auto fill fields when date changes
                $('#date').on('change', function() {
                    let selectedDate = new Date($(this).val());
                    
                    if (!isNaN(selectedDate.getTime())) {
                        // Get day name in Indonesian
                        let dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
                        let dayName = dayNames[selectedDate.getDay()];
                        
                        // Calculate week number
                        let startOfYear = new Date(selectedDate.getFullYear(), 0, 1);
                        let pastDaysOfYear = (selectedDate - startOfYear) / 86400000;
                        let weekNumber = Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
                        
                        // Determine day type
                        let dayType = (selectedDate.getDay() === 0 || selectedDate.getDay() === 6) ? 'weekend' : 'weekdays';
                        
                        // Fill fields
                        $('#day_name').val(dayName);
                        $('#week_number').val(weekNumber);
                        $('#year').val(selectedDate.getFullYear());
                        $('#month').val(selectedDate.getMonth() + 1);
                        $('#day_type').val(dayType);
                    }
                });

                // Trigger change event if date is already filled (for edit mode)
                if ($('#date').val()) {
                    $('#date').trigger('change');
                }

                // Form validation
                $('form').on('submit', function(e) {
                    let isValid = true;
                    
                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });
                    
                    if (!isValid) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'error',
                            title: 'Form Tidak Lengkap!',
                            text: 'Mohon lengkapi semua field yang wajib diisi'
                        });
                    }
                });

                // Clear validation on input change
                $('input, select').on('change', function() {
                    $(this).removeClass('is-invalid');
                });
            });
        </script>
    @endpush
</x-app-layout>
