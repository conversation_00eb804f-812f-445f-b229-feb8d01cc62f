<x-app-layout>
    <x-slot name="header">
        Master Data Pengaturan Cuti
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="" style="width: 300px">
                            <input type="text" class="form-control" id="search-input"
                                placeholder="Cari nama/kode pengaturan cuti" oninput="handleSearch(event)"
                                style="width: 100%" />
                        </div>
                        <div class="ms-md-auto mt-md-0 mt-3">
                            {{-- @if (canPermission('Leave Off Setting.Create')) --}}
                            <div>
                                <a href="{{ route('leave-off-setting.create') }}" class="btn btn-primary">
                                    <i class="feather-plus me-2"></i>
                                    <span>Tambah Pengaturan Cuti</span>
                                </a>
                            </div>
                            {{-- @endif --}}
                        </div>
                    </div>
                    {{-- <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">Informasi Pengaturan Cuti:</h6>
                        <ul class="mb-0">
                            <li><strong>Memotong Jatah:</strong> Cuti yang mengurangi jatah cuti tahunan</li>
                            <li><strong>Tidak Memotong Jatah:</strong> Cuti yang tidak mengurangi jatah cuti tahunan
                            </li>
                        </ul>
                    </div> --}}

                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">No</th>
                                    <th>Nama</th>
                                    <th>Kode</th>
                                    <th>Jenis Cuti</th>
                                    <th>Maksimal Hari</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('components.alert-confirmation')

    @push('scripts')
        @include('libs.datatable')
        <script>
            let table;
            let searchTimeout;

            $(document).ready(function() {
                table = $('#example').DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('leave-off-setting.dataTable') }}",
                        type: "POST",
                        data: function(d) {
                            d._token = "{{ csrf_token() }}";
                            d.keyword = $('#search-input').val();
                        }
                    },
                    columns: [{
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'name',
                            name: 'name'
                        },
                        {
                            data: 'code',
                            name: 'code'
                        },
                        {
                            data: 'leave_type_badge',
                            name: 'leave_type',
                            orderable: false
                        },
                        {
                            data: 'max_leave_days_formatted',
                            name: 'max_leave_days'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ]
                });

                $(document).on('click', '.deleteData', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");
                    const nama = dataInput.name;
                    const urlTarget = `${base_url}/leave-off-setting/${id}`
                    await deleteDataTable(nama, urlTarget, table)
                });
            });

            function handleSearch(event) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.ajax.reload();
                }, 500);
            }

            // Add search input ID for reference
            $(document).ready(function() {
                $('input[placeholder="Cari nama/kode pengaturan cuti"]').attr('id', 'search-input');
            });
        </script>
    @endpush
</x-app-layout>
