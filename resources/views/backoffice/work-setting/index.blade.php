<x-app-layout>
    <x-slot name="header">
        Pengaturan Kerja
    </x-slot>
    <x-slot name="headerRight">
        
    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <label for="branchFilter" class="form-label">Filter Cabang</label>
                            <select class="form-select select2" id="branchFilter">
                                <option value="">Semua Cabang</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="roleFilter" class="form-label">Filter Role</label>
                            <select class="form-select select2" id="roleFilter">
                                <option value="">Semua Role</option>
                                @foreach($roles as $role)
                                    <option value="{{ $role->id }}">{{ $role->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="yearFilter" class="form-label">Tahun</label>
                            <select class="form-select select2" id="yearFilter">
                                @foreach($years as $year)
                                    <option value="{{ $year }}" {{ $year == $currentYear ? 'selected' : '' }}>{{ $year }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="weekFilter" class="form-label">Minggu</label>
                            <select class="form-select select2" id="weekFilter">
                                <!-- Will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="searchEmployee" class="form-label">Cari Karyawan</label>
                            <input type="text" class="form-control" id="searchEmployee" placeholder="Nama karyawan...">
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="button" class="btn btn-primary" id="filterBtn">
                                <i class="feather-filter"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Today's Information -->
                    <div class="alert alert-info" id="todayInfo" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="feather-calendar me-2"></i>
                            <div>
                                <strong>Hari ini:</strong> <span id="todayDate"></span> |
                                <strong>Minggu ke-</strong><span id="todayWeek"></span> |
                                <strong>Tahun:</strong> <span id="todayYear"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Calendar View -->
                    <div class="mt-4" id="calendarView">
                        <!-- Calendar Navigation -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-primary" id="prevWeek">
                                    <i class="feather-chevron-left"></i> Minggu Sebelumnya
                                </button>
                                <button type="button" class="btn btn-outline-success" id="goToToday">
                                    <i class="feather-calendar"></i> Hari Ini
                                </button>
                            </div>
                            <h5 class="mb-0" id="currentWeekTitle">Minggu 1 - 2024</h5>
                            <button type="button" class="btn btn-outline-primary" id="nextWeek">
                                Minggu Selanjutnya <i class="feather-chevron-right"></i>
                            </button>
                        </div>

                        <!-- Calendar Container with horizontal scroll -->
                        <div class="calendar-container">
                            <div id="weeklyCalendar">
                                <!-- Calendar akan di-generate di sini -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <style>
            .calendar-container {
                overflow-x: auto;
                white-space: nowrap;
                max-height: 70vh;
                overflow-y: auto;
            }

            .employee-row {
                border-bottom: 1px solid #dee2e6;
            }

            .employee-info {
                background-color: #f8f9fa !important;
                font-weight: 500;
                position: sticky;
                left: 0;
                z-index: 10;
                min-width: 250px;
                border-right: 2px solid #dee2e6;
            }

            .leave-balance-info {
                background-color: #e3f2fd !important;
                font-weight: 500;
                position: sticky;
                left: 250px;
                z-index: 9;
                min-width: 180px;
                border-right: 2px solid #dee2e6;
                font-size: 0.8rem;
            }

            .date-cell {
                min-width: 150px;
                text-align: center;
                vertical-align: middle;
            }

            .attendance-select {
                width: 100%;
                min-width: 120px;
            }

            .save-btn-container {
                position: sticky;
                top: 0;
                z-index: 100;
                background: white;
                padding: 10px 0;
                border-bottom: 2px solid #dee2e6;
            }

            .table-header {
                position: sticky;
                top: 0;
                z-index: 50;
                background-color: #4a90a4 !important;
                color: white !important;
            }

            .employee-header {
                position: sticky;
                left: 0;
                z-index: 51;
                background-color: #4a90a4 !important;
                color: white !important;
                min-width: 250px;
            }

            .leave-header {
                position: sticky;
                left: 250px;
                z-index: 51;
                background-color: #4a90a4 !important;
                color: white !important;
                min-width: 180px;
                font-size: 0.75rem;
            }

            .full-day-cell {
                background-color: #e8f5e8;
                text-align: center;
                font-weight: bold;
                color: #2e7d32;
            }

            .select2-container {
                width: 100% !important;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            let currentWeek = 1;
            let currentYear = {{ $currentYear }};
            let attendanceTypes = @json($attendanceTypes);
            let workSettingsData = {};
            let searchTimeout;

            $(document).ready(function() {
                // Initialize Select2
                $('.select2').select2({
                    theme: 'bootstrap-5',
                    width: '100%'
                });

                // Get today's information and set current week
                const todayInfo = getTodayInfo();
                currentWeek = todayInfo.weekNumber;
                currentYear = todayInfo.year;

                // Show today's information in UI
                $('#todayDate').text(todayInfo.dateString);
                $('#todayWeek').text(todayInfo.weekNumber);
                $('#todayYear').text(todayInfo.year);
                $('#todayInfo').show();

                // Set year filter to current year
                $('#yearFilter').val(currentYear);

                // Initialize calendar view
                loadCalendarView();
                loadWeekOptions();

                // Event handlers
                $('#branchFilter, #roleFilter').on('change', function() {
                    loadCalendarView();
                });

                $('#searchEmployee').on('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(function() {
                        loadCalendarView();
                    }, 500); // 500ms debounce
                });

                $('#yearFilter').on('change', function() {
                    currentYear = $(this).val();
                    loadWeekOptions();
                    loadCalendarView();
                });

                $('#weekFilter').on('change', function() {
                    currentWeek = $(this).val();
                    loadCalendarView();
                });

                $('#prevWeek').on('click', function() {
                    if (currentWeek > 1) {
                        currentWeek--;
                        $('#weekFilter').val(currentWeek);
                        loadCalendarView();
                    }
                });

                $('#nextWeek').on('click', function() {
                    if (currentWeek < 53) {
                        currentWeek++;
                        $('#weekFilter').val(currentWeek);
                        loadCalendarView();
                    }
                });

                $('#goToToday').on('click', function() {
                    const todayInfo = getTodayInfo();
                    currentWeek = todayInfo.weekNumber;
                    currentYear = todayInfo.year;
                    $('#yearFilter').val(currentYear);
                    $('#weekFilter').val(currentWeek);
                    loadCalendarView();
                });
            });

            function getTodayInfo() {
                const today = new Date();
                const startOfYear = new Date(today.getFullYear(), 0, 1);
                const pastDaysOfYear = (today - startOfYear) / 86400000;
                const weekNumber = Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
                
                return {
                    date: today,
                    dateString: today.toLocaleDateString('id-ID', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    }),
                    weekNumber: weekNumber,
                    year: today.getFullYear()
                };
            }

            function loadWeekOptions() {
                const weekSelect = $('#weekFilter');
                weekSelect.empty();
                
                for (let i = 1; i <= 53; i++) {
                    const selected = i === currentWeek ? 'selected' : '';
                    weekSelect.append(`<option value="${i}" ${selected}>Minggu ${i}</option>`);
                }
            }

            function loadCalendarView() {
                updateCalendarView();
            }

            function loadCalendarView() {
                // Update week title
                $('#currentWeekTitle').text(`Minggu ke-${currentWeek} Tahun ${currentYear}`);

                $.ajax({
                    url: "{{ route('work-setting.getCalendarData') }}",
                    type: 'GET',
                    data: {
                        year: currentYear,
                        week: currentWeek,
                        branch_id: $('#branchFilter').val(),
                        role_id: $('#roleFilter').val(),
                        search: $('#searchEmployee').val()
                    },
                    beforeSend: function() {
                        $('#weeklyCalendar').html(
                            '<div class="text-center p-4"><i class="feather-loader"></i> Memuat kalender...</div>'
                        );
                    },
                    success: function(response) {
                        renderWorkSettingCalendar(response);
                    },
                    error: function() {
                        $('#weeklyCalendar').html(
                            '<div class="text-center p-4 text-danger"><i class="feather-alert-circle"></i> Gagal memuat kalender</div>'
                        );
                    }
                });
            }

            function renderWorkSettingCalendar(data) {
                const { calendar_data, employees, work_settings, attendance_types } = data;

                if (!calendar_data || calendar_data.length === 0) {
                    $('#weeklyCalendar').html('<div class="text-center p-4">Tidak ada data kalender untuk minggu ini</div>');
                    return;
                }

                if (!employees || employees.length === 0) {
                    $('#weeklyCalendar').html('<div class="text-center p-4">Tidak ada karyawan yang sesuai dengan filter</div>');
                    return;
                }

                let html = `
                    <div class="save-btn-container">
                        <button type="button" class="btn btn-success" id="saveAllChanges">
                            <i class="feather-save"></i> Simpan Semua Perubahan
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th class="employee-header">Karyawan</th>
                                    <th class="leave-header">
                                        <div class="row text-center g-0">
                                            <div class="col-4"><small style="font-size: 0.65rem;">Jatah</small></div>
                                            <div class="col-4"><small style="font-size: 0.65rem;">Terpakai</small></div>
                                            <div class="col-4"><small style="font-size: 0.65rem;">Saldo</small></div>
                                        </div>
                                    </th>`;

                // Header dates
                calendar_data.forEach(day => {
                    const date = new Date(day.date);
                    const dayName = date.toLocaleDateString('id-ID', { weekday: 'short' });
                    const dateStr = date.toLocaleDateString('id-ID', { day: '2-digit', month: '2-digit' });
                    html += `<th class="date-cell table-header">${dayName}<br>${dateStr}<br><small>${day.day_type || 'Kerja'}</small></th>`;
                });

                html += `</tr></thead><tbody>`;

                // Employee rows
                employees.forEach(employee => {
                    html += `
                        <tr class="employee-row">
                            <td class="employee-info">
                                <div>
                                    <strong>${employee.nama}</strong>
                                    <br><small class="text-muted">${employee.role?.name || ''} - ${employee.branch?.name || ''}</small>
                                </div>
                            </td>
                            <td class="leave-balance-info">
                                <div class="row text-center g-0">
                                    <div class="col-4">
                                        <span class="badge bg-primary" style="font-size: 0.65rem; padding: 2px 4px;">${employee.leave_balance?.jatah_cuti || 0}</span>
                                    </div>
                                    <div class="col-4">
                                        <span class="badge bg-warning" style="font-size: 0.65rem; padding: 2px 4px;">${employee.leave_balance?.cuti_terpakai || 0}</span>
                                    </div>
                                    <div class="col-4">
                                        <span class="badge bg-success" style="font-size: 0.65rem; padding: 2px 4px;">${employee.leave_balance?.saldo_cuti || 0}</span>
                                    </div>
                                </div>
                            </td>`;

                    calendar_data.forEach(day => {
                        const existingSetting = work_settings[employee.id] && work_settings[employee.id][day.date]
                            ? work_settings[employee.id][day.date][0] : null;

                        // Check if employee needs work setting based on role
                        if (!employee.needs_work_setting) {
                            html += `<td class="date-cell full-day-cell">Full</td>`;
                        } else {
                            html += `
                                <td class="date-cell">
                                    <select class="form-select attendance-select"
                                            data-employee-id="${employee.id}"
                                            data-date="${day.date}">
                                        <option value="">Pilih Jenis Absen</option>`;

                            attendance_types.forEach(type => {
                                const selected = existingSetting && existingSetting.attendance_type_id === type.id ? 'selected' : '';
                                html += `<option value="${type.id}" ${selected}>${type.name}</option>`;
                            });

                            html += `</select></td>`;
                        }
                    });

                    html += `</tr>`;
                });

                html += `</tbody></table></div>`;

                $('#weeklyCalendar').html(html);

                // Initialize Select2 for attendance selects
                $('.attendance-select').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    placeholder: 'Pilih Jenis Absen',
                    allowClear: true
                });

                // Bind save event
                $('#saveAllChanges').on('click', saveAllWorkSettings);
            }

            function saveAllWorkSettings() {
                const workSettings = [];

                $('.attendance-select').each(function() {
                    const employeeId = $(this).data('employee-id');
                    const date = $(this).data('date');
                    const attendanceTypeId = $(this).val();

                    workSettings.push({
                        employee_id: employeeId,
                        date: date,
                        attendance_type_id: attendanceTypeId || null
                    });
                });

                $.ajax({
                    url: "{{ route('work-setting.bulkStore') }}",
                    type: 'POST',
                    data: {
                        _token: "{{ csrf_token() }}",
                        work_settings: workSettings
                    },
                    beforeSend: function() {
                        $('#saveAllChanges').prop('disabled', true).html('<i class="feather-loader"></i> Menyimpan...');
                    },
                    success: function(response) {
                        if (response.status) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil!',
                                text: response.message,
                                timer: 2000,
                                showConfirmButton: false
                            });
                            loadCalendarView(); // Reload to show updated data
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal!',
                                text: response.message
                            });
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: response?.message || 'Gagal menyimpan pengaturan kerja'
                        });
                    },
                    complete: function() {
                        $('#saveAllChanges').prop('disabled', false).html('<i class="feather-save"></i> Simpan Semua Perubahan');
                    }
                });
            }
        </script>
    @endpush
</x-app-layout>
