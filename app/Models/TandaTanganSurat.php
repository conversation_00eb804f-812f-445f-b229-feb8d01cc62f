<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TandaTanganSurat extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    protected $appends = ['barcode_url'];

    public function getBarcodeUrlAttribute()
    {
        return asset($this->barcode);
    }

    public function surat()
    {
        return $this->belongsTo(Surat::class, 'surat_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function referable()
    {
        return $this->morphTo();
    }
}
