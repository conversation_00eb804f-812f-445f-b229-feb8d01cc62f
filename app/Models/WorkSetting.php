<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WorkSetting extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'date' => 'date',
    ];

    // Relations
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function attendanceType()
    {
        return $this->belongsTo(AttendanceTypeSetting::class, 'attendance_type_id');
    }

    // Scopes
    public function scopeFilter($query, $request)
    {
        return $query->when($request->branch_id, function ($q, $branchId) {
            $q->where('branch_id', $branchId);
        })->when($request->role_id, function ($q, $roleId) {
            $q->where('role_id', $roleId);
        })->when($request->employee_id, function ($q, $employeeId) {
            $q->where('employee_id', $employeeId);
        })->when($request->date_from, function ($q, $dateFrom) {
            $q->where('date', '>=', $dateFrom);
        })->when($request->date_to, function ($q, $dateTo) {
            $q->where('date', '<=', $dateTo);
        })->when($request->year, function ($q, $year) {
            $q->whereYear('date', $year);
        })->when($request->month, function ($q, $month) {
            $q->whereMonth('date', $month);
        });
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByRole($query, $roleId)
    {
        return $query->where('role_id', $roleId);
    }

    public function scopeByDate($query, $date)
    {
        return $query->where('date', $date);
    }
}
