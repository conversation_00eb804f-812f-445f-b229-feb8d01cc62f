<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WorkingHourSetting extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'working_hour_type' => 'string',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'is_need_setting_day' => 'boolean',
    ];

    // Constants for working hour types
    const TYPE_MORNING_SHIFT = 'morning_shift';
    const TYPE_EVENING_SHIFT = 'evening_shift';
    const TYPE_NIGHT_SHIFT = 'night_shift';
    const TYPE_FULL_DAY = 'full_day';

    public static function getWorkingHourTypes()
    {
        return [
            self::TYPE_MORNING_SHIFT => 'Shift Pagi',
            self::TYPE_EVENING_SHIFT => 'Shift Siang',
            self::TYPE_NIGHT_SHIFT => 'Shift Malam',
            self::TYPE_FULL_DAY => 'Full Day',
        ];
    }

    public function getWorkingHourTypeNameAttribute()
    {
        $types = self::getWorkingHourTypes();
        return $types[$this->working_hour_type] ?? $this->working_hour_type;
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter) {
            $q->whereHas('role', function ($query) use ($filter) {
                $query->where('name', 'like', '%' . $filter . '%');
            });
        })->when($request->working_hour_type, function ($q, $type) {
            $q->where('working_hour_type', $type);
        })->when($request->role_id, function ($q, $roleId) {
            $q->where('role_id', $roleId);
        });
    }

    public function scopeByRole($query, $roleId)
    {
        return $query->where('role_id', $roleId);
    }

    public function scopeNeedSettingDay($query)
    {
        return $query->where('is_need_setting_day', true);
    }
}
