<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LeaveBranchSetting extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'branch_id' => 'string',
        'max_employee_leave_in_days' => 'integer',
        'approval_levels' => 'array',
    ];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter) {
            $q->whereHas('branch', function ($query) use ($filter) {
                $query->where('name', 'like', '%' . $filter . '%')
                      ->orWhere('code', 'like', '%' . $filter . '%');
            });
        })->when($request->branch_id, function ($q, $branchId) {
            $q->where('branch_id', $branchId);
        });
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }
}
