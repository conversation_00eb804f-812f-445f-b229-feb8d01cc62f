<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LeaveOffSetting extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'leave_type' => 'string',
        'max_leave_days' => 'integer',
    ];

    // Constants for leave types
    const LEAVE_TYPE_CUTTING = 'cutting';
    const LEAVE_TYPE_NON_CUTTING = 'non-cutting';

    public static function getLeaveTypes()
    {
        return [
            self::LEAVE_TYPE_CUTTING => 'Memotong Jatah',
            self::LEAVE_TYPE_NON_CUTTING => 'Tidak Memotong Jatah',
        ];
    }

    public function getLeaveTypeNameAttribute()
    {
        $types = self::getLeaveTypes();
        return $types[$this->leave_type] ?? $this->leave_type;
    }

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter) {
            $q->where('name', 'like', '%' . $filter . '%')
                ->orWhere('code', 'like', '%' . $filter . '%');
        })->when($request->leave_type, function ($q, $type) {
            $q->where('leave_type', $type);
        });
    }

    public function scopeCutting($query)
    {
        return $query->where('leave_type', self::LEAVE_TYPE_CUTTING);
    }

    public function scopeNonCutting($query)
    {
        return $query->where('leave_type', self::LEAVE_TYPE_NON_CUTTING);
    }
}
