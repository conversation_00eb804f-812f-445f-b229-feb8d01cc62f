<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class KopSurat extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];
    protected $appends = ['kop_surat_replaced'];

    public function getKopSuratReplacedAttribute()
    {
        $kop_surat = preg_replace('/src="(?:http:\/\/|https:\/\/)' . preg_quote(request()->getHost(), '/') . '\/(.*?)"/', 'src="' . public_path('$1') . '"', $this->kop_surat);

        return $kop_surat;
    }

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter)  {
            $q->where('nama', 'like', '%' . $filter . '%');
        })->when($request->unit_bisnis_id, function ($q, $unit_bisnis_id) {
            $q->whereJsonContains('unit_bisnis_id', $unit_bisnis_id);
        });
    }
}
