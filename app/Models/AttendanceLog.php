<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AttendanceLog extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    protected $casts = [
        'date_change' => 'date',
        'time_masuk' => 'datetime:H:i',
        'time_keluar' => 'datetime:H:i',
        'user_id' => 'string',
        'branch_id' => 'string',
    ];

    protected $appends = ['date_formatted', 'time_masuk_formatted', 'time_keluar_formatted', 'created_at_formatted', 'updated_at_formatted'];

    /**
     * Get formatted date
     */
    public function getDateFormattedAttribute()
    {
        return format_date_indo($this->date_change);
    }

    /**
     * Get formatted time masuk
     */
    public function getTimeMasukFormattedAttribute()
    {
        return $this->time_masuk ? Carbon::parse($this->time_masuk)->format('H:i') : '-';
    }

    /**
     * Get formatted time keluar
     */
    public function getTimeKeluarFormattedAttribute()
    {
        return $this->time_keluar ? Carbon::parse($this->time_keluar)->format('H:i') : '-';
    }

    /**
     * Get formatted created at
     */
    public function getCreatedAtFormattedAttribute()
    {
        return format_datetime_indo($this->created_at);
    }

    /**
     * Get formatted updated at
     */
    public function getUpdatedAtFormattedAttribute()
    {
        return format_datetime_indo($this->updated_at);
    }

    /**
     * Relationship to User model
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relationship to Branch model
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Scope for filtering
     */
    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter) {
            $q->where('nama', 'like', '%' . $filter . '%')
                ->orWhere('cabang', 'like', '%' . $filter . '%')
                ->orWhere('departemen', 'like', '%' . $filter . '%');
        })->when($request->cabang, function ($q, $cabang) {
            $q->where('cabang', 'like', '%' . $cabang . '%');
        })->when($request->date_from, function ($q, $dateFrom) {
            $q->where('date_change', '>=', $dateFrom);
        })->when($request->date_to, function ($q, $dateTo) {
            $q->where('date_change', '<=', $dateTo);
        });
    }

    /**
     * Scope for specific date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date_change', [$startDate, $endDate]);
    }

    /**
     * Scope for specific branch
     */
    public function scopeByCabang($query, $cabang)
    {
        return $query->where('cabang', $cabang);
    }

    /**
     * Scope for specific employee
     */
    public function scopeByEmployee($query, $nama)
    {
        return $query->where('nama', $nama);
    }
}
