<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Employee extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'hari_off' => 'array',
        'join_date' => 'date',
        'tanggal_lahir' => 'date',
        'resign_date' => 'date',
        'tanggal_deposit' => 'date',
        'tanggal_pengembalian_deposit' => 'date',
        'deposit_seragam' => 'decimal:2',
        'pengembalian_seragam' => 'decimal:2',
        'jumlah_cuti_tahunan' => 'integer',
        'jumlah_seragam' => 'integer',
    ];

    protected $appends = ['avatar_url'];

    public function getAvatarUrlAttribute()
    {
        if ($this->profile_photo) {
            return $this->profile_photo;
        } else {
            $nama = urlencode($this->nama);
            return "https://ui-avatars.com/api/?name={$nama}&color=7F9CF5&background=EBF4FF";
        }
    }

    // Relations
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function customFields()
    {
        return $this->hasMany(EmployeeCustomField::class);
    }

    public function leaveEntitlements()
    {
        return $this->hasMany(EmployeeLeaveEntitlement::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeResigned($query)
    {
        return $query->where('status', 'resigned');
    }

    // Helper methods
    public function getServiceMonthsAttribute()
    {
        if (!$this->join_date) {
            return 0;
        }

        $endDate = $this->resign_date ?? now();
        return $this->join_date->diffInMonths($endDate);
    }

    public function getCurrentYearLeaveEntitlements()
    {
        return $this->leaveEntitlements()
            ->with('leaveOffSetting')
            ->forYear(now()->year)
            ->get();
    }

    public function getLeaveEntitlementsSummary()
    {
        $entitlements = $this->getCurrentYearLeaveEntitlements();
        $summary = [];

        foreach ($entitlements as $entitlement) {
            $summary[] = [
                'type' => $entitlement->leaveOffSetting->name,
                'code' => $entitlement->leaveOffSetting->code,
                'allocated' => $entitlement->allocated_days,
                'used' => $entitlement->used_days,
                'remaining' => $entitlement->remaining_days,
            ];
        }

        return $summary;
    }

    // Boot method to handle model events
    protected static function boot()
    {
        parent::boot();

        static::created(function ($employee) {
            // Allocate leave when employee is created
            if ($employee->status === 'active' && $employee->join_date) {
                app(\App\Services\LeaveAllocationService::class)->allocateLeaveForEmployee($employee);
            }
        });

        static::updated(function ($employee) {
            // Reallocate leave if join_date or status changed
            if ($employee->isDirty(['join_date', 'status']) && $employee->status === 'active' && $employee->join_date) {
                app(\App\Services\LeaveAllocationService::class)->allocateLeaveForEmployee($employee);
            }
        });
    }

    public function scopeFilter($query, $filters)
    {
        return $query->when($filters['keyword'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('nama', 'like', '%'.$search.'%')
                    ->orWhere('kode', 'like', '%'.$search.'%')
                    ->orWhere('nama_asli_ktp', 'like', '%'.$search.'%')
                    ->orWhereHas('role', function ($query) use ($search) {
                        $query->where('name', 'like', '%'.$search.'%');
                    })
                    ->orWhereHas('branch', function ($query) use ($search) {
                        $query->where('name', 'like', '%'.$search.'%');
                    });
            });
        })->when($filters['status'] ?? null, function ($query, $status) {
            $query->where('status', $status);
        })->when($filters['branch_id'] ?? null, function ($query, $branchId) {
            $query->where('branch_id', $branchId);
        })->when($filters['role_id'] ?? null, function ($query, $roleId) {
            $query->where('role_id', $roleId);
        });
    }

    // Accessors
    public function getFormattedJoinDateAttribute()
    {
        return $this->join_date ? $this->join_date->format('d/m/Y') : '-';
    }

    public function getFormattedTanggalLahirAttribute()
    {
        return $this->tanggal_lahir ? $this->tanggal_lahir->format('d/m/Y') : '-';
    }

    public function getFormattedResignDateAttribute()
    {
        return $this->resign_date ? $this->resign_date->format('d/m/Y') : '-';
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => '<span class="badge bg-success px-3 py-2 rounded-full">Aktif</span>',
            'inactive' => '<span class="badge bg-warning px-3 py-2 rounded-full">Tidak Aktif</span>',
            'resigned' => '<span class="badge bg-danger px-3 py-2 rounded-full">Resign</span>',
        ];

        return $badges[$this->status] ?? '<span class="badge bg-secondary px-3 py-2 rounded-full">Unknown</span>';
    }

    public function getGenderLabelAttribute()
    {
        return $this->gender === 'male' ? 'Laki-laki' : ($this->gender === 'female' ? 'Perempuan' : '-');
    }

    public function getHariOffFormattedAttribute()
    {
        if (!$this->hari_off || !is_array($this->hari_off)) {
            return '-';
        }

        $days = [
            'monday' => 'Senin',
            'tuesday' => 'Selasa',
            'wednesday' => 'Rabu',
            'thursday' => 'Kamis',
            'friday' => 'Jumat',
            'saturday' => 'Sabtu',
            'sunday' => 'Minggu'
        ];

        $formatted = [];
        foreach ($this->hari_off as $day) {
            if (isset($days[$day])) {
                $formatted[] = $days[$day];
            }
        }

        return implode(', ', $formatted);
    }

    public function getMasaKerjaHariAttribute()
    {
        if (!$this->join_date) {
            return 0;
        }

        return $this->join_date->diffInDays(now());
    }

    public function getMasaKerjaFormattedAttribute()
    {
        $days = $this->masa_kerja_hari;

        if ($days == 0) {
            return '0 hari';
        }

        $years = floor($days / 365);
        $months = floor(($days % 365) / 30);
        $remainingDays = $days % 30;

        $result = [];
        if ($years > 0) {
            $result[] = $years . ' tahun';
        }
        if ($months > 0) {
            $result[] = $months . ' bulan';
        }
        if ($remainingDays > 0) {
            $result[] = $remainingDays . ' hari';
        }

        return implode(', ', $result);
    }

    public function getJumlahCutiTahunanDinamis()
    {
        $entitlements = $this->getCurrentYearLeaveEntitlements();

        if ($entitlements->isEmpty()) {
            // If no entitlements found, try to allocate them
            app(\App\Services\LeaveAllocationService::class)->allocateLeaveForEmployee($this);
            $entitlements = $this->getCurrentYearLeaveEntitlements();
        }

        // Sum up all allocated days for annual leave types
        $totalDays = $entitlements->sum('allocated_days');

        return $totalDays ?: 0;
    }
}
