<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Notification extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    protected $appends = ['diff_for_humans', 'is_read'];

    public function getIsReadAttribute()
    {
        return $this->read_at ? true : false;
    }

    public function getDiffForHumansAttribute()
    {
        return $this->created_at ? $this->created_at->diffForHumans() : $this->created_at;
    }
}
