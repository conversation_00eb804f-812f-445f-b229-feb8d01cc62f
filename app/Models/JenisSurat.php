<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class JenisSurat extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter)  {
            $q->where('jenis_surat', 'like', '%' . $filter . '%')
                ->orWhere('kode', 'like', '%' . $filter . '%')
                ->orWhere('type', 'like', '%' . $filter . '%');
        });
    }
}
