<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SignerDocumentEsign extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    public function documentEsign()
    {
        return $this->belongsTo(DocumentEsign::class);
    }

    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    public function annotations()
    {
        return $this->hasMany(AnnotationDocumentEsign::class);
    }
}
