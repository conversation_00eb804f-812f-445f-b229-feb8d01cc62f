<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role as SpatieRole;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Role extends SpatieRole
{
    use HasFactory, HasUuids;
    protected $table = 'roles';
    protected $guarded = ['id'];

    public function userRoles()
    {
        return $this->belongsToMany(User::class, 'model_has_roles', 'role_id', 'model_id');
    }

    public function scopeFilter()
    {

    }

    public function scopeFilterFromSurat($query)
    {

        if((canPermission('Disposisi.Full_Akses') ?? false) || (canPermission('Disposisi.Corsec') ?? false) || (canPermission('Surat Masuk.Full_Akses') ?? false))
            return $query;

        // condition checker
        if(canPermission('Disposisi.Checker') ?? false) {
            $query->whereHas('permissions', function($query) {
                $query->where('name', 'Disposisi.Approver');
            });
        }
        //condition approver
        else if(canPermission('Disposisi.Approver') ?? false) {
            $query->whereHas('permissions', function($query) {
                $query->where('name', 'Disposisi.Corsec');
            });
        }
        // condition maker
        else {
            $query->whereHas('permissions', function($query) {
                $query->where('name', 'Disposisi.Checker');
            });
        }

        return $query;
    }
}
