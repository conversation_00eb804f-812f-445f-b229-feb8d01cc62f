<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EmployeeLeaveEntitlement extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'year' => 'integer',
        'allocated_days' => 'integer',
        'used_days' => 'integer',
        'remaining_days' => 'integer',
        'valid_from' => 'date',
        'valid_until' => 'date',
    ];

    // Relations
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function leaveOffSetting()
    {
        return $this->belongsTo(LeaveOffSetting::class);
    }

    // Scopes
    public function scopeForYear($query, $year)
    {
        return $query->where('year', $year);
    }

    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeActive($query)
    {
        $today = now()->toDateString();
        return $query->where(function($q) use ($today) {
            $q->whereNull('valid_from')
              ->orWhere('valid_from', '<=', $today);
        })->where(function($q) use ($today) {
            $q->whereNull('valid_until')
              ->orWhere('valid_until', '>=', $today);
        });
    }

    // Mutators
    public function setAllocatedDaysAttribute($value)
    {
        $this->attributes['allocated_days'] = $value;
        $this->updateRemainingDays();
    }

    public function setUsedDaysAttribute($value)
    {
        $this->attributes['used_days'] = $value;
        $this->updateRemainingDays();
    }

    // Helper methods
    private function updateRemainingDays()
    {
        if (isset($this->attributes['allocated_days']) && isset($this->attributes['used_days'])) {
            $this->attributes['remaining_days'] = $this->attributes['allocated_days'] - $this->attributes['used_days'];
        }
    }

    public function canUseLeave($days)
    {
        return $this->remaining_days >= $days;
    }

    public function useLeave($days)
    {
        if ($this->canUseLeave($days)) {
            $this->used_days += $days;
            $this->save();
            return true;
        }
        return false;
    }
}
