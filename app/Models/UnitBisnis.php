<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UnitBisnis extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter)  {
            $q->where('nama', 'like', '%' . $filter . '%');
        });
    }
}
