<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasUuids, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    protected $appends = ['avatar_url'];

    public function getAvatarUrlAttribute()
    {
        if ($this->profile_photo) {
            return $this->profile_photo;
        } else {
            $nama = urlencode($this->nama);
            return "https://ui-avatars.com/api/?name={$nama}&color=7F9CF5&background=EBF4FF";
        }
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function userAccessBranches()
    {
        return $this->hasMany(UserAccessBranch::class);
    }

    public function branches()
    {
        return $this->belongsToMany(Branch::class, 'user_access_branches');
    }

    public function scopeFilter($query, $filters)
    {
        return $query->when($filters['keyword'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('nama', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%')
                    ->orWhereHas('role', function ($query) use ($search) {
                        $query->where('name', 'like', '%'.$search.'%');
                    });
            });
        });
    }
}
