<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Surat extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    public const STATUS_DRAFT = 'Draft';
    public const STATUS_DIPROSES = 'Diproses';
    public const STATUS_DISPOSISI = 'Disposisi';
    public const STATUS_SELESAI = 'Selesai';
    public const STATUS_EXPIRED = 'Kadaluarsa';
    public const STATUS_REVISI = 'Revisi';

    public const TYPE_SURAT = [
        'surat_keluar' => 1,
        'reply' => 2,
        'forward' => 3,
        'revision' => 4,
    ];

    public const APPROVE_STATUS = [
        'disetujui' => 1,
        'disetujui_dengan_pertimbangan' => 2,
        'tidak_disetujui' => 3,
    ];

    protected $appends = ['created_at_human', 'date_formatted', 'status_formatted', 'approve_status_name', 'approve_status_name_formatted', 'check_surat_expired', 'date_update_formatted', 'latest_revision_formatted'];

    public function getLatestRevisionFormattedAttribute()
    {
        return $this->latest_revision_at ? Carbon::parse($this->latest_revision_at)->translatedFormat('d F Y H:i') : $this->latest_revision_at;
    }

    public function getCheckSuratExpiredAttribute()
    {
        return $this->parent_surat_id == null && ($this->childSurat->count() ?? 0) == 0 && ($this->created_at ? $this->created_at->diffInDays(now()) : '') >= 2;
    }

    public function getApproveStatusNameAttribute()
    {
        $value = array_search($this->approve_status, self::APPROVE_STATUS);
        return $this->approve_status ? ucwords(str_replace('_', ' ', $value)) : null;
    }

    public function jenisSurat()
    {
        return $this->belongsTo(JenisSurat::class, 'jenis_surat_id');
    }

    public function getApproveStatusNameFormattedAttribute()
    {
        $value = array_search($this->approve_status, self::APPROVE_STATUS);
        $approve_format =  $this->approve_status ? ucwords(str_replace('_', ' ', $value)) : null;

        return approve_status($this->approve_status, $approve_format);
    }

    public function getCreatedAtHumanAttribute()
    {
        return $this->created_at ? $this->created_at->diffForHumans() : $this->created_at;
    }

    public function getDateFormattedAttribute()
    {
        return $this->created_at ? Carbon::parse($this->created_at)->translatedFormat('d F Y H:i') : $this->created_at;
    }

    public function getDateUpdateFormattedAttribute()
    {
        return $this->updated_at ?  Carbon::parse($this->updated_at)->translatedFormat('d F Y H:i') : $this->updated_at;
    }

    public function getStatusFormattedAttribute()
    {
        if ($this->check_surat_expired) {
            $this->status = self::STATUS_EXPIRED;
        }
        return status_surat($this->status);
    }

    public function childSurat()
    {
        return $this->hasMany(Surat::class, 'parent_surat_id');
    }

    public function parentSurat()
    {
        return $this->belongsTo(Surat::class, 'parent_surat_id');
    }

    public function pengirim()
    {
        return $this->belongsTo(User::class, 'dikirim_oleh_id');
    }

    public function tujuanSurat()
    {
        return $this->hasMany(TujuanSurat::class, 'surat_id');
    }

    public function attachmentFileSurat()
    {
        return $this->hasMany(AttachmentFileSurat::class);
    }

    public function contentSurat()
    {
        return $this->hasOne(ContentSurat::class, 'id', 'content_surat_id');
    }

    public function parentContentSurat()
    {
        return $this->hasOne(ContentSurat::class, 'id', 'parent_content_surat_id');
    }

    public function history()
    {
        return $this->hasMany(HistorySurat::class);
    }

    public function kopSurat()
    {
        return $this->belongsTo(KopSurat::class);
    }

    public function tandaTanganSurat()
    {
        return $this->hasMany(TandaTanganSurat::class);
    }

    public function tandaTanganSuratReferable()
    {
        return $this->morphMany(TandaTanganSurat::class, 'referable');
    }

    public function readSurat()
    {
        return $this->hasOne(ReadSurat::class, 'surat_id');
    }

    public function requestEsign()
    {
        return $this->hasOne(RequestEsign::class);
    }

    public function unitBisnis()
    {
        return $this->belongsTo(UnitBisnis::class);
    }

    public function unitBisnisSurat()
    {
        return $this->hasMany(UnitBisnisSurat::class, 'surat_id');
    }

    public function replySurat()
    {
        return $this->hasMany(Surat::class, 'reply_to', 'id');
    }

    public function scopeFilter($query, $filter)
    {
        $query->when($filter->surat_expired ?? null, function ($query) {
            $query->where('parent_surat_id', null)
                ->whereDoesntHave('childSurat')
                ->where('created_at', '<=', now()->subDays(2));
        })
            ->when($filter->start_date ?? false, function ($query) use ($filter) {
                $query->whereDate('created_at', '>=', $filter->start_date);
            })
            ->when($filter->end_date ?? false, function ($query) use ($filter) {
                $query->whereDate('created_at', '<=', $filter->end_date);
            })
            ->when($filter->unit_bisnis_id ?? false, function ($query) use ($filter) {
                $query->where('unit_bisnis_id', $filter->unit_bisnis_id);
            })
            ->when($filter->keyword ?? false, function ($query) use ($filter) {
                $query->where(function ($query) use ($filter) {
                    $query->where('no_surat', 'like', '%' . $filter->keyword . '%')
                        ->orWhere('perihal', 'like', '%' . $filter->keyword . '%')
                        ->orWhereHas('pengirim', function ($query) use ($filter) {
                            $query->where('nama', 'like', '%' . $filter->keyword . '%');
                        })
                        ->orWhereHas('unitBisnis', function ($query) use ($filter) {
                            $query->where('nama', 'like', '%' . $filter->keyword . '%');
                        });
                });
            })->when($filter->status ?? false, function ($query) use ($filter) {
                switch ($filter->status) {
                    case 'not_process':
                        return $query->where(function ($query) {
                            $query->whereDoesntHave('childSurat', function ($query) {
                                $query->where('created_by_id', auth()->user()->id);
                            })
                            ->orWhereDoesntHave('childSurat', function ($query) {
                                $query
                                    ->where('created_by_id', auth()->user()->id)
                                    ->whereHas('replySurat', function ($subQuery) {
                                        $subQuery->where('status', self::STATUS_REVISI)
                                            ->whereHas('replySurat', function ($subTwoQuery) {
                                                $subTwoQuery->where('created_by_id', auth()->user()->id);
                                            });
                                    });
                            });
                        })
                            ->where('status', '!=', self::STATUS_SELESAI);
                    case 'done_process':
                        return $query->whereHas('childSurat', function ($query) {
                            $query->where('created_by_id', auth()->user()->id);
                        })
                            ->where('status', '!=', self::STATUS_SELESAI);
                    case 'done':
                        return $query->where('status', self::STATUS_SELESAI);
                    case 'all':
                        return $query;
                    default:
                        return $query;
                }
            });
    }
}
