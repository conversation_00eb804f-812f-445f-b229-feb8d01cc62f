<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AttendanceRequest extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    protected $casts = [
        'attendance_date' => 'date',
        'approved_at' => 'datetime',
        'user_id' => 'string',
        'employee_id' => 'string',
        'branch_id' => 'string',
        'attendance_type_id' => 'string',
        'approved_by' => 'string',
    ];

    protected $appends = ['attendance_date_formatted', 'status_badge', 'approved_at_formatted', 'created_at_formatted', 'updated_at_formatted'];

    /**
     * Get formatted attendance date
     */
    public function getAttendanceDateFormattedAttribute()
    {
        return format_date_indo($this->attendance_date);
    }

    /**
     * Get formatted approved at
     */
    public function getApprovedAtFormattedAttribute()
    {
        return format_datetime_indo($this->approved_at);
    }

    /**
     * Get formatted created at
     */
    public function getCreatedAtFormattedAttribute()
    {
        return format_datetime_indo($this->created_at);
    }

    /**
     * Get formatted updated at
     */
    public function getUpdatedAtFormattedAttribute()
    {
        return format_datetime_indo($this->updated_at);
    }

    /**
     * Get status badge
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => '<span class="badge bg-warning px-3 py-2 rounded-full">Menunggu Persetujuan</span>',
            'approved' => '<span class="badge bg-success px-3 py-2 rounded-full">Disetujui</span>',
            'rejected' => '<span class="badge bg-danger px-3 py-2 rounded-full">Ditolak</span>',
        ];

        return $badges[$this->status] ?? '<span class="badge bg-secondary px-3 py-2 rounded-full">Unknown</span>';
    }

    /**
     * Relationship to user who created the request
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Relationship to Employee model
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    /**
     * Relationship to branch
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Relationship to attendance type
     */
    public function attendanceType()
    {
        return $this->belongsTo(AttendanceTypeSetting::class, 'attendance_type_id');
    }

    /**
     * Relationship to user who approved/rejected
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope for filtering based on user's accessible branches
     */
    public function scopeAccessibleByUser($query, $userId)
    {
        return $query->whereHas('branch.userAccessBranches', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        });
    }

    /**
     * Scope for filtering
     */
    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter) {
            $q->whereHas('employee', function ($query) use ($filter) {
                $query->where('nama', 'like', '%' . $filter . '%');
            })->orWhereHas('branch', function ($query) use ($filter) {
                $query->where('name', 'like', '%' . $filter . '%');
            })->orWhere('reason', 'like', '%' . $filter . '%');
        })->when($request->branch_id, function ($q, $branchId) {
            $q->where('branch_id', $branchId);
        })->when($request->status, function ($q, $status) {
            $q->where('status', $status);
        })->when($request->attendance_type_id, function ($q, $typeId) {
            $q->where('attendance_type_id', $typeId);
        })->when($request->date_from, function ($q, $dateFrom) {
            $q->where('attendance_date', '>=', $dateFrom);
        })->when($request->date_to, function ($q, $dateTo) {
            $q->where('attendance_date', '<=', $dateTo);
        })->when($request->employee_user_id, function ($q, $employeeId) {
            $q->where('employee_user_id', $employeeId);
        });
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved requests
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected requests
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope for specific date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('attendance_date', [$startDate, $endDate]);
    }

    /**
     * Check if request can be edited
     */
    public function canBeEdited()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if request can be processed (approved/rejected)
     */
    public function canBeProcessed()
    {
        return $this->status === 'pending';
    }



    /**
     * Approve the request
     */
    public function approve($approvedBy)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
            'rejection_reason' => null,
        ]);
    }

    /**
     * Reject the request
     */
    public function reject($rejectedBy, $reason)
    {
        $this->update([
            'status' => 'rejected',
            'approved_by' => $rejectedBy,
            'approved_at' => now(),
            'rejection_reason' => $reason,
        ]);
    }
}
