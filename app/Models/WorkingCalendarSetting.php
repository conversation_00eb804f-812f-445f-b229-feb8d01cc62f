<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WorkingCalendarSetting extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'date' => 'date',
        'week_number' => 'integer',
        'day_type' => 'string',
        'is_holiday' => 'boolean',
        'year' => 'integer',
        'month' => 'integer',
    ];

    protected $appends = ['date_formatted', 'day_type_name'];

    // Constants for day types
    const DAY_TYPE_WEEKDAYS = 'weekdays';
    const DAY_TYPE_WEEKEND = 'weekend';

    public static function getDayTypes()
    {
        return [
            self::DAY_TYPE_WEEKDAYS => 'Weekdays',
            self::DAY_TYPE_WEEKEND => 'Weekend',
        ];
    }

    public function getDateFormattedAttribute()
    {
        return $this->date ? Carbon::parse($this->date)->translatedFormat('d F Y') : null;
    }

    public function getDayTypeNameAttribute()
    {
        $types = self::getDayTypes();
        return $types[$this->day_type] ?? $this->day_type;
    }

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter) {
            $q->where('day_name', 'like', '%' . $filter . '%');
        })->when($request->year, function ($q, $year) {
            $q->where('year', $year);
        })->when($request->month, function ($q, $month) {
            $q->where('month', $month);
        })->when($request->day_type, function ($q, $dayType) {
            $q->where('day_type', $dayType);
        })->when($request->is_holiday !== null, function ($q) use ($request) {
            $q->where('is_holiday', $request->is_holiday);
        });
    }

    public function scopeByYear($query, $year)
    {
        return $query->where('year', $year);
    }

    public function scopeByMonth($query, $month)
    {
        return $query->where('month', $month);
    }

    public function scopeHolidays($query)
    {
        return $query->where('is_holiday', true);
    }

    public function scopeWeekends($query)
    {
        return $query->where('day_type', self::DAY_TYPE_WEEKEND);
    }

    public function scopeWeekdays($query)
    {
        return $query->where('day_type', self::DAY_TYPE_WEEKDAYS);
    }

    public function scopeWorkingDays($query)
    {
        return $query->where('day_type', self::DAY_TYPE_WEEKDAYS)
                    ->where('is_holiday', false);
    }
}
