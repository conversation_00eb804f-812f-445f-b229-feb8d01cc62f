<?php

namespace App\Jobs;

use App\Mail\SendEmailSurat;
use App\Models\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SendEmailSuratJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    protected $dataItem;
    protected $dataSurat;
    protected $subject;
    public function __construct(
        $dataItem,
        $dataSurat,
        $subject
    )
    {
        $this->dataItem = $dataItem;
        $this->dataSurat = $dataSurat;
        $this->subject = $subject;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $dataItem = $this->dataItem;
        $dataSurat = $this->dataSurat;
        $subject = $this->subject;

        Notification::create([
            'user_id' => $dataItem->id,
            'surat_id' => $dataSurat->id,
            'message' => $subject,
        ]);
    }
}
