<?php

namespace App\Http\Services;

use GuzzleHttp\Client;
use App\Models\Setting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class eSignService
{
    public function connect()
    {

        try {
            $setting = getSettingGroup('esign');

            $api_sso_url_mekari = $setting['api_sso_url_mekari'] ?? '';
            $client_id = $setting['client_id_mekari'] ?? '';
            $client_secret = $setting['client_secret_mekari'] ?? '';
            $code_esign = $setting['code_esign'] ?? '';

            if(!$api_sso_url_mekari || !$client_id || !$client_secret || !$code_esign) {
                return [
                    'status' => false,
                    'message' => 'Not access'
                ];
            }

            $client_secret = decrypt($client_secret);

            $response = Http::post("{$api_sso_url_mekari}/oauth2/token", [
                'client_id' => $client_id,
                'client_secret' => $client_secret,
                'grant_type' => 'authorization_code',
                'code' => $code_esign
            ]);

            $data = $response->json();

            if(isset($data['error'])) {
                return $this->refreshToken();
            }

            DB::transaction(function() use ($data) {
                Setting::updateOrCreate(
                    ['key' => 'access_token_esign', 'group' => 'esign'],
                    ['value' => encrypt($data['access_token'])]
                );
                Setting::updateOrCreate(
                    ['key' => 'refresh_token_esign', 'group' => 'esign'],
                    ['value' => encrypt($data['refresh_token'])]
                );
                Setting::updateOrCreate(
                    ['key' => 'token_type_esign', 'group' => 'esign'],
                    ['value' => $data['token_type']]
                );
                Setting::updateOrCreate(
                    ['key' => 'expires_in_esign', 'group' => 'esign'],
                    ['value' => $data['expires_in']]
                );
            });

            return [
                'status' => true,
                'data' => $data
            ];

        } catch (\Exception $e) {
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }

    }

    public function refreshToken()
    {

        try {
            $setting = getSettingGroup('esign');

            $api_sso_url_mekari = $setting['api_sso_url_mekari'] ?? '';
            $client_id = $setting['client_id_mekari'] ?? '';
            $client_secret = $setting['client_secret_mekari'] ?? '';
            $code_esign = $setting['code_esign'] ?? '';
            $refresh_token = $setting['refresh_token_esign'] ?? '';

            if(!$api_sso_url_mekari || !$client_id || !$client_secret || !$code_esign || !$refresh_token) {
                return [
                    'status' => false,
                    'message' => 'API URL not found'
                ];
            }

            $client_secret = decrypt($client_secret);
            $refresh_token = decrypt($refresh_token);
            $response = Http::post("{$api_sso_url_mekari}/oauth2/token", [
                'client_id' => $client_id,
                'client_secret' => $client_secret,
                'grant_type' => 'refresh_token',
                'refresh_token' => $refresh_token
            ]);

            $data = $response->json();

            if(isset($data['error'])) {
                return [
                    'status' => false,
                    'message' => $data['error_description']
                ];
            }

            DB::transaction(function() use ($data) {
                Setting::updateOrCreate(
                    ['key' => 'access_token_esign', 'group' => 'esign'],
                    ['value' => encrypt($data['access_token'])]
                );
                Setting::updateOrCreate(
                    ['key' => 'refresh_token_esign', 'group' => 'esign'],
                    ['value' => encrypt($data['refresh_token'])]
                );
                Setting::updateOrCreate(
                    ['key' => 'token_type_esign', 'group' => 'esign'],
                    ['value' => $data['token_type']]
                );
                Setting::updateOrCreate(
                    ['key' => 'expires_in_esign', 'group' => 'esign'],
                    ['value' => $data['expires_in']]
                );
            });

            return [
                'status' => true,
                'data' => $data
            ];

        } catch (\Exception $e) {
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }

    }

    public function getProfile()
    {
        return $this->fetch('get', 'profile');
    }

    public function fetch($method = 'get', $path, $params = [], $options = [
        'is_response' => false
    ])
    {
        $setting = getSettingGroup('esign');
        $access_token = $setting['access_token_esign'] ?? '';
        $api_url_mekari = $setting['api_url_mekari'] ?? '';
        if(!$access_token) {
            return [
                'status' => false,
                'message' => 'Access token not found'
            ];
        }

        $access_token = decrypt($access_token);

        try {
            $response = Http::withToken($access_token)->$method("$api_url_mekari/$path", $params);
            if(isset($options['is_response'])) {
                $data = $response;
            } else {
                $data = $response->json();
            }

            if(!$data) {
                return [
                    'status' => false,
                    'message' => 'Data not found'
                ];
            }

            if(($data['message'] ?? '') === 'Unauthorized') {
                $refresh_token = $this->refreshToken();
                if(!($refresh_token['status'] ?? false)) {
                    return $refresh_token;
                }

                return $this->fetch($method, $path, $params);
            }

            if($options['is_response']) {
                return $data;
            }

            return [
                'status' => true,
                'data' => $data['data'] ?? []
            ];
        } catch (\Exception $e) {
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function documentList($request)
    {
        $page = ($request->start / $request->length) + 1;
        $params = [
            'page' => $page,
            'limit' => $request->length ?? 0,
            'category' => $request->category ?? '',
            'signing_status' => $request->status ?? '',
            'subject' => $request->subject
        ];

        return $this->fetch('get', 'documents', $params, [
            'is_response' => true
        ]);
    }

    public function movToTrashDocument($id)
    {
        return $this->fetch('delete', "documents/{$id}/delete");
    }

    public function resendDocument($id)
    {
        return $this->fetch('post', "documents/{$id}/resend");
    }


}
