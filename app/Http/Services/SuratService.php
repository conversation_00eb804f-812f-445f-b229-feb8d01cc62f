<?php

namespace App\Http\Services;

use App\Models\User;
use App\Models\Surat;
use Ilovepdf\Ilovepdf;
use App\Mail\SendEmailSurat;
use App\Models\HistorySurat;
use App\Models\Notification;
use App\Jobs\SendEmailSuratJob;
use Illuminate\Support\Facades\DB;
use App\Models\AttachmentFileSurat;
use Illuminate\Support\Facades\Log;
use App\Models\ApprovalHistorySurat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class SuratService
{
    public function detailData($id)
    {
        $data = Surat::with([
            'pengirim:id,nama,unit_bisnis_id,email,role_id',
            'pengirim.unitBisnis:id,nama',
            'pengirim.role:id,name',
            'contentSurat',
            'history' => function ($query) {
                $query->orderBy('created_at', 'desc');
            },
            'history.user:id,nama,email',
            'history.role:id,name',
            'tujuanSurat:id,role_id,surat_id,user_id',
            'tujuanSurat.user:id,nama,email',
            'tujuanSurat.role:id,name',
            'tujuanSurat.role.userRoles:id,nama,email,role_id',
            'attachmentFileSurat',
            'childSurat' => function ($query) {
                $query->where(function ($q) {
                    $q->where('dikirim_oleh_id', Auth::user()->id)
                        ->orWhere('dikirim_oleh_role_id', Auth::user()->role_id)
                        ->orWhereHas('tujuanSurat', function ($subQuery) {
                            $subQuery->where('role_id', Auth::user()->role_id)
                                    ->orWhere('user_id', Auth::user()->id);
                        });
                });
            },
            'childSurat.pengirim:id,nama,unit_bisnis_id,email,role_id',
            'childSurat.pengirim.unitBisnis:id,nama',
            'childSurat.parentContentSurat',
            'childSurat.pengirim.role:id,name',
            'childSurat.contentSurat',
            'childSurat.contentSurat',
            'childSurat.tujuanSurat:id,role_id,surat_id,user_id',
            'childSurat.tujuanSurat.user:id,nama,email',
            'childSurat.tujuanSurat.role:id,name',
            'childSurat.tujuanSurat.role.userRoles:id,nama,email,role_id',
            'childSurat.attachmentFileSurat',
            'tandaTanganSurat',
            'requestEsign',
            'requestEsign.signers',
        ])
            ->where(function ($query) {
                if(canPermission('Surat Kadaluarsa.List'))
                    return;

                $query->where('dikirim_oleh_id', Auth::user()->id)
                    ->orWhere('dikirim_oleh_role_id', Auth::user()->role_id)
                    ->orWhereHas('tujuanSurat', function ($q) {
                        $q->where('role_id', Auth::user()->role_id)
                            ->orWhere('user_id', Auth::user()->id);
                    })->orWhereHas('childSurat', function ($q) {
                        $q->where('dikirim_oleh_id', Auth::user()->id)
                            ->orWhereHas('tujuanSurat', function ($subQuery) {
                                $subQuery->where('role_id', Auth::user()->role_id)
                                    ->orWhere('user_id', Auth::user()->id);
                            });
                    });
            })
            ->findOrFail($id);
        return $data;
    }

    public function saveAttachment($surat, $attachment)
    {
        $existingAttachments = AttachmentFileSurat::where('surat_id', $surat->id)->get();

        $newAttachmentIds = array_column($attachment, 'id');
        $attachmentsToDelete = $existingAttachments->whereNotIn('id', $newAttachmentIds);
        AttachmentFileSurat::whereIn('id', $attachmentsToDelete->pluck('id'))->delete();

        foreach ($attachment as $value) {
            $existingAttachment = $existingAttachments->firstWhere('id', $value['id']);
            if ($existingAttachment) {
                $existingAttachment->update([
                    'file' => $value['data']['url'] ?? $existingAttachment->file,
                    'nama_original' => $value['data']['name_original'] ?? $existingAttachment->nama_original,
                    'size' => $value['data']['size'] ?? $existingAttachment->size,
                    'required_signature' => $value['data']['required_signature'] ?? $existingAttachment->required_signature,
                ]);
            } else {
                $surat->attachmentFileSurat()->create([
                    'file' => $value['data']['url'] ?? '',
                    'nama_original' => $value['data']['name_original'] ?? '',
                    'file_convert' => $value['data']['file_convert'] ?? '',
                    'size' => $value['data']['size'] ?? '',
                    'required_signature' => $value['data']['required_signature'] ?? false,
                    'created_by_id' => Auth::id(),
                ]);
            }
        }

        return $surat;
    }

    public function saveTujuanSurat($surat, $tujuan_surat)
    {
        $tujuan_surat_input = [];
        foreach ($tujuan_surat as $key => $value) {
            $tujuan_surat_input[] = [
                'user_id' => $value['user_id'] ?? '',
                'role_id' => $value['role_id'] ?? '',
                'created_by_id' => Auth::id(),
            ];
        }
        $surat->tujuanSurat()->createMany($tujuan_surat_input);
        return $surat;
    }

    public function createLogHistory($surat_id, $input = [
        'status' => 'Created',
        'type' => 'Surat Masuk',
        'approval_status' => null,
        'unit_bisnis_id' => null,
    ])
    {
        HistorySurat::create([
            'surat_id' => $surat_id,
            'user_id' => Auth::id(),
            'role_id' => Auth::user()->role_id,
            'status' => $input['status'],
            'type' => $input['type'] ?? '',
            'approval_status' => $input['approval_status'] ?? null,
            'unit_bisnis_id' => $input['unit_bisnis_id'] ?? null,
        ]);
    }

    public function createLogApproval($surat_id, $input = [
        'approval_status' => 'Approval',
        'status' => 1,
        'unit_bisnis_id' => null,
    ])
    {
        ApprovalHistorySurat::create([
            'surat_id' => $surat_id,
            'user_id' => Auth::id(),
            'role_id' => Auth::user()->role_id,
            'approval_status' => $input['approval_status'],
            'status' => $input['status'],
            'unit_bisnis_id' => $input['unit_bisnis_id'] ?? null,
        ]);
    }

    public function sendNotificationSurat(
        $dataSurat,
        $subject,
        $tujuan_id = [
            'role_id' => [],
            'user_id' => [],
            'unit_bisnis_id' => [],
        ]
    )
    {
        if(count($tujuan_id) < 1) {
            return;
        }

        $user = User::when($tujuan_id['unit_bisnis_id'], function ($q, $filter) {
                    $q->where(function($q) use ($filter) {
                        $q->whereHas('unitBisnis', function ($q) use ($filter) {
                            $q->whereIn('id', $filter);
                        })->orWhereHas('userUnitBisnis', function ($q) use ($filter) {
                            $q->whereIn('unit_bisnis_id', $filter);
                        });
                    });
                })
                ->whereIn('role_id', $tujuan_id['role_id'])
                ->get();
        $user = $user->merge(User::whereIn('id', $tujuan_id['user_id'])->get());

        DB::transaction(function () use ($user, $dataSurat, $subject) {
            foreach($user as $item) {
                Log::info('Send Email Notification to ' . $item->email);

                Mail::to($item->email)->queue(new SendEmailSurat($dataSurat, $subject, $item));
                SendEmailSuratJob::dispatch( $item, $dataSurat, $subject);
            }
        });

        return true;

    }

    public function convertFileToPdf($filePath)
    {
        $ilovepdf = new Ilovepdf(getSettingValue('ilove_pdf_public_key'), getSettingValue('ilove_pdf_secret_key'));

        $task = $ilovepdf->newTask('officepdf');

        $task->addFile($filePath);
        $task->execute();

        $outputFilePath = 'uploads/file-manager/' . pathinfo($filePath, PATHINFO_FILENAME) . '.pdf';
        $task->download(public_path('uploads/file-manager'));

        return $outputFilePath;
    }
}

