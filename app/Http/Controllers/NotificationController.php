<?php

namespace App\Http\Controllers;

use App\Models\HistorySurat;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    public function index()
    {
        return view('backoffice.notification.index');
    }

    public function list(Request $request)
    {
        $notifications = Notification::where('user_id', Auth::user()->id)
                                        ->when($request->keyword, function ($query) use ($request) {
                                            return $query->where('message', 'like', "%{$request->keyword}%");
                                        })
                                        ->latest()
                                        ->paginate($request->per_page ?? 6);

        return response()->json([
            'status' => true,
            'data' => $notifications,
        ]);
    }

    public function countNoRead()
    {
        $count = Notification::where('user_id', Auth::user()->id)
                                ->whereNull('read_at')
                                ->where('is_read', false)
                                ->count();
        return response()->json([
            'status' => true,
            'data' => $count,
        ]);
    }

    public function makeAsRead(Request $request)
    {
        $notification = Notification::where('user_id', Auth::user()->id)
                                    ->when($request->id, function ($query) use ($request) {
                                        return $query->where('id', $request->id);
                                    })
                                    ->update([
                                        'read_at' => now(),
                                        'is_read' => true,
                                    ]);

        return response()->json([
            'status' => true,
            'message' => 'Berhasil menandai notifikasi sebagai sudah dibaca',
        ]);
    }
}
