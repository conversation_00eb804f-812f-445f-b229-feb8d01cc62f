<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Branch;
use Illuminate\Http\Request;
use App\Models\AttendanceLog;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Yajra\DataTables\Facades\DataTables;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class AttendanceLogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.attendance-log.index');
    }

    /**
     * DataTable for attendance logs
     */
    public function dataTable(Request $request)
    {
        $data = AttendanceLog::select(
            'id',
            'cabang',
            'nama',
            'departemen',
            'date_change',
            'time_masuk',
            'time_keluar',
            'created_at'
        )
        ->latest()
        ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('checkbox', function ($data) {
                return '<input type="checkbox" class="form-check-input row-checkbox" value="' . $data->id . '">';
            })
            ->addColumn('date_formatted', function ($data) {
                return $data->date_formatted;
            })
            ->addColumn('time_masuk_formatted', function ($data) {
                return $data->time_masuk_formatted;
            })
            ->addColumn('time_keluar_formatted', function ($data) {
                return $data->time_keluar_formatted;
            })
            ->editColumn('created_at', function ($data) {
                return $data->created_at_formatted;
            })
            ->addColumn('action', function ($data) {
                $action_button = '';

                if(canPermission('Upload Log Absensi.Delete')) {
                    $action_button .= "<li>
                                        <a class='dropdown-item deleteData' href='javascript:void(0)' data-id='$data->id' data-input='".json_encode($data)."'>
                                            <i class='feather feather-trash-2 me-3'></i>
                                            <span>Delete</span>
                                        </a>
                                    </li>";
                }

                if ($action_button) {
                    $action = "<div class='hstack gap-2'>
                                <div class='dropdown dropdown-overflow'>
                                    <a href='javascript:void(0)'
                                        class='avatar-text avatar-md btn-dropdown'
                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                        <i class='feather feather-more-horizontal'></i>
                                    </a>
                                    <ul class='dropdown-menu'>
                                        $action_button
                                    </ul>
                                </div>
                            </div>";
                    return $action;
                }

                return '-';
            })
            ->rawColumns(['checkbox', 'action'])
            ->make(true);
    }

    /**
     * Upload Excel file for attendance logs
     */
    public function uploadExcel(Request $request)
    {
        $request->validate([
            'attendance_file' => 'required|file|mimes:xlsx,xls|max:2048'
        ]);

        try {
            $file = $request->file('attendance_file');
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            $imported = 0;
            $updated = 0;
            $errors = [];
            $successRows = [];
            $failedRows = [];

            DB::transaction(function () use ($rows, &$imported, &$updated, &$errors, &$successRows, &$failedRows) {
                foreach ($rows as $i => $row) {
                    // Skip header row and empty rows
                    if ($i === 0 || empty(array_filter($row))) {
                        continue;
                    }

                    try {
                        // Map Excel columns based on the provided format
                        $cabang = trim($row[0] ?? '');
                        $nama = trim($row[1] ?? '');
                        $departemen = trim($row[2] ?? '');
                        $dateChange = trim($row[3] ?? '');
                        $timeMasuk = trim($row[4] ?? '');
                        $timeKeluar = trim($row[5] ?? '');

                        // Skip if required fields are empty
                        if (empty($cabang) || empty($nama) || empty($dateChange)) {
                            continue;
                        }

                        // Parse date
                        $date = null;
                        if (!empty($dateChange)) {
                            // Try different date formats
                            $dateFormats = ['d/m/Y', 'Y-m-d', 'd-m-Y', 'm/d/Y'];
                            foreach ($dateFormats as $format) {
                                try {
                                    $date = Carbon::createFromFormat($format, $dateChange);
                                    break;
                                } catch (\Exception $e) {
                                    continue;
                                }
                            }
                            
                            if (!$date) {
                                $errorMsg = "Format tanggal tidak valid ($dateChange)";
                                $errors[] = "Baris " . ($i + 1) . ": $errorMsg";
                                $failedRows[] = [
                                    'row' => $i + 1,
                                    'data' => "$cabang | $nama | $departemen | $dateChange | $timeMasuk | $timeKeluar",
                                    'error' => $errorMsg
                                ];
                                continue;
                            }
                        }

                        // Parse time fields
                        $parsedTimeMasuk = null;
                        $parsedTimeKeluar = null;

                        if (!empty($timeMasuk) && $timeMasuk !== '-') {
                            // Handle different time formats from Excel
                            $timeMasuk = trim($timeMasuk);

                            // Try parsing with different formats including AM/PM
                            $timeFormats = [
                                'g:i:s A',    // 9:17:00 AM
                                'h:i:s A',    // 09:17:00 AM
                                'g:i A',      // 9:17 AM
                                'h:i A',      // 09:17 AM
                                'H:i:s',      // 09:17:00 (24-hour)
                                'H:i',        // 09:17 (24-hour)
                                'G:i:s',      // 9:17:00 (24-hour)
                                'G:i',        // 9:17 (24-hour)
                                'H.i.s',      // 09.17.00
                                'H.i',        // 09.17
                                'G.i.s',      // 9.17.00
                                'G.i'         // 9.17
                            ];

                            foreach ($timeFormats as $format) {
                                try {
                                    $parsedTimeMasuk = Carbon::createFromFormat($format, $timeMasuk)->format('H:i:s');
                                    break;
                                } catch (\Exception $e) {
                                    continue;
                                }
                            }

                            // If still null, try to extract numbers and format
                            if (!$parsedTimeMasuk) {
                                $numbers = preg_replace('/[^0-9]/', '', $timeMasuk);
                                if (strlen($numbers) >= 3) {
                                    $hour = substr($numbers, 0, -2);
                                    $minute = substr($numbers, -2);
                                    if ($hour <= 23 && $minute <= 59) {
                                        $parsedTimeMasuk = sprintf('%02d:%02d:00', $hour, $minute);
                                    }
                                }
                            }
                        }

                        if (!empty($timeKeluar) && $timeKeluar !== '-') {
                            // Handle different time formats from Excel
                            $timeKeluar = trim($timeKeluar);

                            // Try parsing with different formats including AM/PM
                            $timeFormats = [
                                'g:i:s A',    // 9:17:00 AM
                                'h:i:s A',    // 09:17:00 AM
                                'g:i A',      // 9:17 AM
                                'h:i A',      // 09:17 AM
                                'H:i:s',      // 09:17:00 (24-hour)
                                'H:i',        // 09:17 (24-hour)
                                'G:i:s',      // 9:17:00 (24-hour)
                                'G:i',        // 9:17 (24-hour)
                                'H.i.s',      // 09.17.00
                                'H.i',        // 09.17
                                'G.i.s',      // 9.17.00
                                'G.i'         // 9.17
                            ];

                            foreach ($timeFormats as $format) {
                                try {
                                    $parsedTimeKeluar = Carbon::createFromFormat($format, $timeKeluar)->format('H:i:s');
                                    break;
                                } catch (\Exception $e) {
                                    continue;
                                }
                            }

                            // If still null, try to extract numbers and format
                            if (!$parsedTimeKeluar) {
                                $numbers = preg_replace('/[^0-9]/', '', $timeKeluar);
                                if (strlen($numbers) >= 3) {
                                    $hour = substr($numbers, 0, -2);
                                    $minute = substr($numbers, -2);
                                    if ($hour <= 23 && $minute <= 59) {
                                        $parsedTimeKeluar = sprintf('%02d:%02d:00', $hour, $minute);
                                    }
                                }
                            }
                        }

                        // Validate user and branch
                        $user = User::where('nama', $nama)->first();
                        $branch = Branch::where('name', $cabang)->first();

                        // Check if user exists
                        if (!$user) {
                            $errorMsg = "User '$nama' tidak ditemukan di sistem";
                            $errors[] = "Baris " . ($i + 1) . ": $errorMsg";
                            $failedRows[] = [
                                'row' => $i + 1,
                                'data' => "$cabang | $nama | $departemen | $dateChange | $timeMasuk | $timeKeluar",
                                'error' => $errorMsg
                            ];
                            continue;
                        }

                        // Check if branch exists
                        if (!$branch) {
                            $errorMsg = "Cabang '$cabang' tidak ditemukan di sistem";
                            $errors[] = "Baris " . ($i + 1) . ": $errorMsg";
                            $failedRows[] = [
                                'row' => $i + 1,
                                'data' => "$cabang | $nama | $departemen | $dateChange | $timeMasuk | $timeKeluar",
                                'error' => $errorMsg
                            ];
                            continue;
                        }

                        // Check if user has access to this branch
                        $hasAccess = $user->branches()->where('branch_id', $branch->id)->exists();
                        if (!$hasAccess) {
                            $errorMsg = "User '$nama' tidak memiliki akses ke cabang '$cabang'";
                            $errors[] = "Baris " . ($i + 1) . ": $errorMsg";
                            $failedRows[] = [
                                'row' => $i + 1,
                                'data' => "$cabang | $nama | $departemen | $dateChange | $timeMasuk | $timeKeluar",
                                'error' => $errorMsg
                            ];
                            continue;
                        }

                        // Check if record exists (for update functionality)
                        $existingLog = AttendanceLog::where('nama', $nama)
                            ->where('cabang', $cabang)
                            ->where('date_change', $date->format('Y-m-d'))
                            ->first();

                        $logData = [
                            'cabang' => $cabang,
                            'nama' => $nama,
                            'departemen' => $departemen,
                            'date_change' => $date->format('Y-m-d'),
                            'time_masuk' => $parsedTimeMasuk,
                            'time_keluar' => $parsedTimeKeluar,
                            'user_id' => $user ? $user->id : null,
                            'branch_id' => $branch ? $branch->id : null,
                        ];

                        if ($existingLog) {
                            $existingLog->update($logData);
                            $updated++;
                            $successRows[] = [
                                'row' => $i + 1,
                                'data' => "$cabang | $nama | $departemen | $dateChange | $timeMasuk | $timeKeluar",
                                'action' => 'Updated'
                            ];
                        } else {
                            AttendanceLog::create($logData);
                            $imported++;
                            $successRows[] = [
                                'row' => $i + 1,
                                'data' => "$cabang | $nama | $departemen | $dateChange | $timeMasuk | $timeKeluar",
                                'action' => 'Created'
                            ];
                        }

                    } catch (\Exception $e) {
                        $errors[] = "Baris " . ($i + 1) . ": " . $e->getMessage();
                        continue;
                    }
                }
            });

            $totalProcessed = $imported + $updated;
            $totalFailed = count($failedRows);

            $message = "Upload selesai: {$totalProcessed} berhasil ({$imported} baru, {$updated} update)";
            if ($totalFailed > 0) {
                $message .= ", {$totalFailed} gagal";
            }

            return response()->json([
                'status' => true,
                'message' => $message,
                'imported' => $imported,
                'updated' => $updated,
                'total_success' => $totalProcessed,
                'total_failed' => $totalFailed,
                'success_rows' => $successRows,
                'failed_rows' => $failedRows,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Gagal upload file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download Excel template
     */
    public function downloadTemplate()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers based on the provided Excel format
        $sheet->setCellValue('A1', 'Cabang');
        $sheet->setCellValue('B1', 'Nama');
        $sheet->setCellValue('C1', 'Departemen');
        $sheet->setCellValue('D1', 'Date Change');
        $sheet->setCellValue('E1', 'Time Masuk');
        $sheet->setCellValue('F1', 'Time Keluar');

        // Add sample data
        $sheet->setCellValue('A2', 'Cabang1');
        $sheet->setCellValue('B2', 'Aca131');
        $sheet->setCellValue('C2', 'SPG');
        $sheet->setCellValue('D2', '11/05/2025');
        $sheet->setCellValue('E2', '09.17.00');
        $sheet->setCellValue('F2', '21.31.00');

        $sheet->setCellValue('A3', 'Cabang1');
        $sheet->setCellValue('B3', 'Aca131');
        $sheet->setCellValue('C3', 'SPG');
        $sheet->setCellValue('D3', '12/05/2025');
        $sheet->setCellValue('E3', '09.19.00');
        $sheet->setCellValue('F3', '21.32.00');

        $sheet->setCellValue('A4', 'Cabang2');
        $sheet->setCellValue('B4', 'Ade243');
        $sheet->setCellValue('C4', 'SPG');
        $sheet->setCellValue('D4', '11/05/2025');
        $sheet->setCellValue('E4', '08.46.00');
        $sheet->setCellValue('F4', '21.31.00');

        // Style the header row
        $sheet->getStyle('A1:F1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'color' => ['argb' => 'FFFF00']
            ]
        ]);

        // Auto size columns
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'template_attendance_log_' . date('Y-m-d') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $attendanceLog = AttendanceLog::findOrFail($id);

            DB::transaction(function () use ($attendanceLog) {
                $attendanceLog->delete();
            });

            return response()->json(['status' => true, 'message' => 'Data berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Bulk delete selected records
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:attendance_logs,id'
        ]);

        try {
            $deletedCount = 0;

            DB::transaction(function () use ($request, &$deletedCount) {
                $deletedCount = AttendanceLog::whereIn('id', $request->ids)->delete();
            });

            return response()->json([
                'status' => true,
                'message' => "Berhasil menghapus {$deletedCount} data log absensi"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Delete all records with optional filters
     */
    public function deleteAll(Request $request)
    {
        try {
            $query = AttendanceLog::query();

            // Apply same filters as dataTable
            $query->filter($request);

            $totalCount = $query->count();

            if ($totalCount === 0) {
                return response()->json([
                    'status' => false,
                    'message' => 'Tidak ada data yang ditemukan untuk dihapus'
                ], 400);
            }

            DB::transaction(function () use ($query) {
                $query->delete();
            });

            return response()->json([
                'status' => true,
                'message' => "Berhasil menghapus semua {$totalCount} data log absensi"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }
}
