<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class RolePermissionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $permission = Permission::orderBy('name', 'asc')->get();
        return view('backoffice.role-permission.index', compact('permission'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $permission = Permission::orderBy('name', 'asc')->get();
        return view('backoffice.role-permission.create-update', compact('permission'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if($request->id)
            $request->validate([
                'name' => 'required|unique:roles,name,' . $request->id,
                'permission' => 'required',
            ]);
        else
            $request->validate([
                'name' => 'required|unique:roles,name',
                'permission' => 'required',
            ]);

        if($request->id){
            $role = Role::find($request->id);
            $role->name = $request->name;
            $role->save();
        }else{
            $role = Role::create(['name' => $request->name, 'guard_name' => 'web']);
        }

        $permission = $request->permission;
        if($permission){
            $role->revokePermissionTo($permission);
            $role->syncPermissions($permission);
        }else{
            $role->revokePermissionTo($permission);
        }

        return redirect(route('role-permission.index'))->with('success', 'Data berhasil disimpan!')->withInput();
    }

    public function storePermission(Request $request)
    {
        $id = $request->permission_id;
        $request->validate([
            'name' => $request->permission_id ? 'required|unique:permissions,name,'.$request->permission_id : 'required|unique:permissions,name',
        ]);


        $input = $request->all();

        try {
            DB::transaction(function() use ($input, $id, $request){
                $input['guard_name'] = 'web';
                Permission::updateOrCreate(['id' => $id], $input);
            });

            return response()->json(['status' => true,'message' => 'Data berhasil disimpan'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Role::findOrFail($id);
        $permission = Permission::orderBy('name', 'asc')->get();
        return view('backoffice.role-permission.create-update', compact('permission', 'data'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(string $id)
    {
        try {
            $role = Role::findOrFail($id);
            DB::transaction(function() use ($role){
                $role->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function destroyPermission(string $id)
    {
        try {
            $permission = Permission::findOrFail($id);
            DB::transaction(function() use ($permission){
                $permission->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = Role::select(
            'id',
            'name',
            'created_at',
            'updated_at'
        )
            ->latest()
            ->filter($request);

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) {
                $action = "
                    <div class='d-flex align-items-center gap-3'>
                        <a href='".route('role-permission.edit', $data->id)."'
                            class='avatar-text avatar-md'>
                            <i class='feather feather-edit-3'></i>
                        </a>
                       <a href='javascript:void(0)'
                            data-id='".$data->id."'
                            data-input='".$data."'
                            class='avatar-text avatar-md deleteData bg-danger text-white'>
                            <i class='feather feather-trash-2'></i>
                        </a>
                    </div>
                ";
                return $action;
            })
            ->rawColumns(['action'])
            ->smart(true)
            ->make(true);
    }

    public function dataTablePermission(Request $request)
    {
        $data = Permission::select(
            'id',
            'name',
            'created_at',
            'updated_at'
        )
            ->latest();

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('action', function ($data) {
                $action = "
                    <div class='d-flex align-items-center gap-3'>
                        <div role='button'
                            data-id='".$data->id."'
                            data-input='".$data."'
                            class='avatar-text avatar-md editInput'>
                            <i class='feather feather-edit-3'></i>
                        </div>
                        <a href='javascript:void(0)'
                            data-id='".$data->id."'
                            data-input='".$data."'
                            class='avatar-text avatar-md deleteDataPermission bg-danger text-white'>
                            <i class='feather feather-trash-2'></i>
                        </a>
                    </div>
                ";
                return $action;
            })
            ->rawColumns(['action'])
            ->smart(true)
            ->make(true);
    }
}
