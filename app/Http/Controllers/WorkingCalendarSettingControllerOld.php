<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\WorkingCalendarSetting;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Yajra\DataTables\Facades\DataTables;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class WorkingCalendarSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $currentYear = date('Y');
        $years = range($currentYear - 2, $currentYear + 2);
        return view('backoffice.working-calendar-setting.index', compact('years', 'currentYear'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $dayTypes = WorkingCalendarSetting::getDayTypes();
        return view('backoffice.working-calendar-setting.create-update', compact('dayTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'date' => [
                'required',
                'date',
                Rule::unique('working_calendar_settings', 'date')->ignore($id)->whereNull('deleted_at'),
            ],
            'day_name' => 'nullable|string|max:255',
            'week_number' => 'required|integer|min:1|max:53',
            'day_type' => 'required|in:weekdays,weekend',
            'is_holiday' => 'boolean',
            'year' => 'required|integer|min:2020|max:2050',
            'month' => 'required|integer|min:1|max:12',
        ];

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $date = Carbon::parse($request->date);
            
            $input = [
                'date' => $request->date,
                'day_name' => $request->day_name ?: $date->translatedFormat('l'),
                'week_number' => $request->week_number ?: $date->weekOfYear,
                'day_type' => $request->day_type,
                'is_holiday' => $request->boolean('is_holiday'),
                'year' => $request->year ?: $date->year,
                'month' => $request->month ?: $date->month,
            ];

            WorkingCalendarSetting::updateOrCreate([
                'id' => $id,
            ], $input);
        });

        return redirect(route('working-calendar-setting.index'))->with('success', 'Pengaturan kalender kerja berhasil disimpan');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $data = WorkingCalendarSetting::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = WorkingCalendarSetting::findOrFail($id);
        $dayTypes = WorkingCalendarSetting::getDayTypes();
        return view('backoffice.working-calendar-setting.create-update', compact('data', 'dayTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validator = [
            'date' => 'required|date|unique:working_calendar_settings,date,' . $id,
            'day_type' => 'required|in:weekdays,weekend',
            'is_holiday' => 'boolean'
        ];

        $request->validate($validator);

        try {
            $workingCalendarSetting = WorkingCalendarSetting::findOrFail($id);

            $date = Carbon::parse($request->date);

            DB::transaction(function() use ($workingCalendarSetting, $request, $date) {
                $workingCalendarSetting->update([
                    'date' => $date->format('Y-m-d'),
                    'day_name' => $date->translatedFormat('l'),
                    'week_number' => $date->weekOfYear,
                    'day_type' => $request->day_type,
                    'is_holiday' => $request->has('is_holiday'),
                    'year' => $date->year,
                    'month' => $date->month,
                ]);
            });

            return response()->json(['status' => true,'message' => 'Data berhasil diperbarui'], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => false,'message' => 'Gagal memperbarui data: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $workingCalendarSetting = WorkingCalendarSetting::findOrFail($id);

            DB::transaction(function() use ($workingCalendarSetting) {
                $workingCalendarSetting->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = WorkingCalendarSetting::select(
            'id',
            'date',
            'day_name',
            'week_number',
            'day_type',
            'is_holiday',
            'year',
            'month',
            'created_at'
        )
        ->when($request->week, function($q) use ($request) {
            $q->where('week_number', $request->week);
        })
        ->latest('date')
        ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('date_formatted', function ($data) {
                return $data->date_formatted;
            })
            ->addColumn('day_type_badge', function ($data) {
                $badgeClass = $data->day_type === 'weekdays' ? 'bg-primary' : 'bg-warning';
                return "<span class='badge {$badgeClass} px-3 py-2 rounded-full'>{$data->day_type_name}</span>";
            })
            ->addColumn('is_holiday_badge', function ($data) {
                $badgeClass = intval($data->is_holiday) === 1 ?  'bg-danger' : 'bg-success';
                $text = intval($data->is_holiday) === 1 ? 'Ya' : 'Tidak';
                return "<span class='badge {$badgeClass} px-3 py-2 rounded-full'>{$text}</span>";
            })
            ->addColumn('action', function ($data) {
                $action_button = '';
                
                if(canPermission('Working Calendar Setting.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item' href='".route('working-calendar-setting.edit', $data->id)."'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>";
                }

                if(canPermission('Working Calendar Setting.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='".json_encode($data)."'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action', 'day_type_badge', 'is_holiday_badge'])
            ->smart(true)
            ->make(true);
    }

    /**
     * Generate calendar for a specific year
     */
    public function generateCalendar(Request $request)
    {
        $request->validate([
            'year' => 'required|integer|min:2020|max:2050'
        ]);

        $year = $request->year;
        $generated = 0;

        DB::transaction(function () use ($year, &$generated) {
            $startDate = Carbon::createFromDate($year, 1, 1);
            $endDate = Carbon::createFromDate($year, 12, 31);
            $currentDate = $startDate->copy();
            $dayCount = 0;

            while ($currentDate->lte($endDate)) {
                // Calculate week number (setiap 7 hari = minggu baru)
                // Day 0-6 = Week 1, Day 7-13 = Week 2, dst
                $weekNumber = intval($dayCount / 7) + 1;

                $existing = WorkingCalendarSetting::where('date', $currentDate->format('Y-m-d'))->first();
                
                if (!$existing) {
                    WorkingCalendarSetting::create([
                        'date' => $currentDate->format('Y-m-d'),
                        'day_name' => $currentDate->translatedFormat('l'),
                        'week_number' => $weekNumber,
                        'day_type' => NULL,
                        // 'day_type' => $currentDate->isWeekend() ? 'weekend' : 'weekdays',
                        'is_holiday' => false,
                        'year' => $currentDate->year,
                        'month' => $currentDate->month,
                    ]);
                    $generated++;
                }
                
                $currentDate->addDay();
                $dayCount++;
            }
        });

        return response()->json([
            'status' => true,
            'message' => "Berhasil generate {$generated} hari untuk tahun {$year}",
            'generated' => $generated
        ]);
    }

    /**
     * Get calendar data for display
     */
    public function getCalendarData(Request $request)
    {
        $year = $request->year ?: date('Y');
        $week = $request->week ?: 1;

        // Validasi minggu (maksimal 53 minggu dalam setahun)
        if ($week < 1) $week = 1;
        if ($week > 53) $week = 53;

        // Generate calendar data automatically if not exists
        $this->generateWeekData($year, $week);

        // Hitung tanggal awal dan akhir untuk minggu yang diminta
        $startOfYear = Carbon::createFromDate($year, 1, 1);
        $startDate = $startOfYear->copy()->addDays(($week - 1) * 7);
        $endDate = $startDate->copy()->addDays(6);

        // Jika tanggal akhir sudah di tahun berikutnya, batasi sampai akhir tahun
        if ($endDate->year > $year) {
            $endDate = Carbon::createFromDate($year, 12, 31);
        }

        $query = WorkingCalendarSetting::select('id', 'date', 'day_name', 'day_type', 'is_holiday', 'week_number')
                                      ->byYear($year)
                                      ->whereBetween('date', [
                                          $startDate->format('Y-m-d'),
                                          $endDate->format('Y-m-d')
                                      ]);

        $data = $query->orderBy('date')->get();

        // Jika data kosong, mungkin belum ada di database, generate ulang
        if ($data->isEmpty()) {
            $this->generateWeekData($year, $week);
            $data = $query->orderBy('date')->get();
        }

        return response()->json($data);
    }

    /**
     * Generate week data automatically
     * Setiap minggu selalu 7 hari:
     * Week 1 = 1-7 Januari, Week 2 = 8-14 Januari, dst
     */
    private function generateWeekData($year, $week)
    {
        // Minggu 1 dimulai dari 1 Januari
        $startOfYear = Carbon::createFromDate($year, 1, 1);

        // Hitung tanggal mulai minggu yang diminta
        // Minggu 1 = 1-7 Januari, Minggu 2 = 8-14 Januari, dst
        $startDate = $startOfYear->copy()->addDays(($week - 1) * 7);

        // Generate tepat 7 hari untuk minggu ini
        for ($i = 0; $i < 7; $i++) {
            $currentDate = $startDate->copy()->addDays($i);

            // Pastikan masih dalam tahun yang sama
            if ($currentDate->year !== $year) {
                break;
            }

            // Check if this date already exists
            $existing = WorkingCalendarSetting::where('date', $currentDate->format('Y-m-d'))->first();

            if (!$existing) {
                // Tentukan day_type berdasarkan hari dalam seminggu
                $dayOfWeek = $currentDate->dayOfWeek; // 0=Sunday, 1=Monday, ..., 6=Saturday
                $isWeekend = ($dayOfWeek == 0 || $dayOfWeek == 6); // Sunday atau Saturday
                $dayType = $isWeekend ? 'weekend' : 'weekdays';

                WorkingCalendarSetting::create([
                    'date' => $currentDate->format('Y-m-d'),
                    'day_name' => $currentDate->translatedFormat('l'), // Nama hari dalam bahasa Indonesia
                    'week_number' => $week,
                    'day_type' => $dayType,
                    'is_holiday' => false, // Default false, bisa diubah via Excel
                    'year' => $currentDate->year,
                    'month' => $currentDate->month,
                ]);
            } else {
                // Update week_number jika data sudah ada tapi week_number berbeda
                if ($existing->week_number != $week) {
                    $existing->update(['week_number' => $week]);
                }
            }
        }
    }

    /**
     * Upload Excel file for calendar
     */
    public function uploadExcel(Request $request)
    {
        $request->validate([
            'calendar_file' => 'required|file|mimes:xlsx,xls|max:2048'
        ]);

        try {
            $file = $request->file('calendar_file');
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            $imported = 0;
            $errors = [];

            DB::transaction(function () use ($rows, &$imported, &$errors) {
                // Skip header row
                for ($i = 1; $i < count($rows); $i++) {
                    $row = $rows[$i];

                    // Skip empty rows
                    if (empty($row[0]) && empty($row[1])) continue;

                    // Skip rows where column A is empty (no date)
                    if (empty($row[0])) continue;

                    // Skip rows where column B is empty (no day type)
                    if (empty($row[1])) continue;

                    // Skip instruction rows (rows that start with text like "Petunjuk:", "-", etc.)
                    $cellA = trim($row[0]);
                    if (preg_match('/^(petunjuk|instruksi|-|\*|note|catatan|keterangan)/i', $cellA)) {
                        continue;
                    }

                    try {
                        // Try to parse date from column A
                        $date = Carbon::parse($row[0]);

                        // Get day type from column B (Jenis_Hari)
                        $dayType = trim($row[1]);

                        // Skip if day type is empty after trimming
                        if (empty($dayType)) continue;

                        // Validate day type
                        if (!in_array(strtolower($dayType), ['weekdays', 'weekend', 'holiday'])) {
                            // Skip invalid day types instead of throwing error (could be instruction text)
                            continue;
                        }

                        $dayType = strtolower($dayType);

                        // Auto-calculate day name and week number
                        $dayName = $date->translatedFormat('l');
                        $weekNumber = $date->weekOfYear;

                        // Determine if it's a holiday
                        $isHoliday = ($dayType === 'holiday');

                        WorkingCalendarSetting::updateOrCreate(
                            ['date' => $date->format('Y-m-d')],
                            [
                                'day_name' => $dayName,
                                'week_number' => $weekNumber,
                                'day_type' => $dayType,
                                'is_holiday' => $isHoliday,
                                'year' => $date->year,
                                'month' => $date->month,
                            ]
                        );

                        $imported++;
                    } catch (\Exception $e) {
                        // Skip rows that can't be parsed as dates (likely instruction rows)
                        // Only log actual parsing errors for valid-looking data
                        if (preg_match('/^\d{4}-\d{2}-\d{2}/', trim($row[0]))) {
                            $errors[] = "Baris " . ($i + 1) . ": " . $e->getMessage();
                        }
                        continue;
                    }
                }
            });

            $message = "Berhasil import {$imported} data kalender";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(', ', array_slice($errors, 0, 3));
            }

            return response()->json([
                'status' => true,
                'message' => $message,
                'imported' => $imported,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Gagal upload file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download Excel template
     */
    public function downloadTemplate()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers - simplified to only 2 columns
        $sheet->setCellValue('A1', 'Tanggal');
        $sheet->setCellValue('B1', 'Jenis_Hari');

        // Add sample data with various day types
        $sheet->setCellValue('A2', '2025-01-01');
        $sheet->setCellValue('B2', 'weekdays');

        $sheet->setCellValue('A3', '2025-01-02');
        $sheet->setCellValue('B3', 'weekdays');

        $sheet->setCellValue('A4', '2025-01-03');
        $sheet->setCellValue('B4', 'holiday');

        $sheet->setCellValue('A5', '2025-01-06');
        $sheet->setCellValue('B5', 'weekend');

        $sheet->setCellValue('A6', '2025-01-07');
        $sheet->setCellValue('B6', 'weekend');

        // Add instructions in separate rows
        $sheet->setCellValue('A8', 'Petunjuk:');
        $sheet->setCellValue('A9', '- Format tanggal: YYYY-MM-DD (contoh: 2024-01-01)');
        $sheet->setCellValue('A10', '- Jenis hari yang valid: weekdays, weekend, holiday');
        $sheet->setCellValue('A11', '- Minggu ke dan nama hari akan otomatis dihitung sistem');

        // Style headers
        $sheet->getStyle('A1:B1')->getFont()->setBold(true);
        $sheet->getStyle('A1:B1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        $sheet->getStyle('A1:B1')->getFill()->getStartColor()->setARGB('FFCCCCCC');

        // Style instructions
        $sheet->getStyle('A8:A11')->getFont()->setItalic(true);
        $sheet->getStyle('A8')->getFont()->setBold(true);

        // Auto size columns
        foreach (range('A', 'B') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $writer = new Xlsx($spreadsheet);

        $filename = 'template_calendar_' . date('Y-m-d') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    /**
     * Bulk update day types for multiple dates
     */
    public function bulkUpdate(Request $request)
    {
        try {
            $changes = $request->input('changes', []);
            $updatedCount = 0;

            foreach ($changes as $change) {
                $date = date('Y-m-d', strtotime($change['date']));
                $calendar = WorkingCalendarSetting::whereDate('date', $date)->first();
                if ($calendar) {
                    $calendar->update([
                        'day_type' => $change['day_type'],
                        'is_holiday' => $change['is_holiday'] === "true",
                    ]);

                    $updatedCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Berhasil mengupdate {$updatedCount} data",
                'updated_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }
}
