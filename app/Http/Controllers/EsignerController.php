<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use App\Models\RequestEsign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Services\eSignService;
use App\Models\RequestEsignSigners;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;


class EsignerController extends Controller
{

    public function __construct(
        private eSignService $eSignService
    )
    {
        $this->eSignService = $eSignService;
    }

    public function webhook(Request $request)
    {
        $data = $request->all();
        Log::info('Webhook data: ' . json_encode($data));

        return response()->json([
            'status' => true,
            'message' => 'Webhook received'
        ]);
    }

    public function index()
    {
        $setting = Setting::where('group', 'esign')->pluck('value', 'key');
        $profile = $this->eSignService->getProfile()['data']['attributes'] ?? null;
        return view('backoffice.pengaturan.e-sign.index', compact('setting', 'profile'));
    }

    public function store(Request $request)
    {
        $input = $request->all();
        unset($input['_token']);
        foreach ($input as $key => $value) {
            if($key === 'client_secret_mekari') {
                if(!$value)
                    $value = getSettingValue('client_secret_mekari');
                else
                    $value = encrypt($value);
            }
            Setting::updateOrCreate(
                ['key' => $key, 'group' => 'esign'],
                ['value' => $value]
            );
        }

        return redirect(route('pengaturan.e-sign.index'))->with('success', 'Pengaturan berhasil disimpan');
    }

    public function webhookSigning(Request $request)
    {
        $response = $request->all();

        Log::info('Webhook signing data: ' . json_encode($response));

        $data = $response['data'] ?? null;
        $attribute = $data['attributes'] ?? null;
        if(!$attribute) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid data'
            ]);
        }

        DB::transaction(function () use ($data, $attribute) {
            $request_esign = RequestEsign::where('request_id', $data['id'])->first();
            if(!$request_esign) {
                return response()->json([
                    'status' => false,
                    'message' => 'Request eSign not found'
                ]);
            }

            $request_esign->signing_status = $attribute['signing_status'];
            $request_esign->request_updated_at = $attribute['updated_at'];

            $signers = $attribute['signers'] ?? [];
            foreach ($signers as $item) {
                $signer = RequestEsignSigners::where(['email' => $item['email'], 'request_esign_id' => $request_esign->id])->first();
                if($signer) {
                    $signer->status = $item['status'];
                    $signer->signed_at = $item['signed_at'];
                    $signer->save();
                }
            }
        });

        return response()->json([
            'status' => true,
            'message' => 'Webhook signing received',
            'data' => $data
        ]);
    }

    public function tryConnectEsign(Request $request)
    {
        try {
            $response = $this->eSignService->connect();
            return response()->json([
                'status' => true,
                'message' => 'Connected to eSign successfully',
                'data' => $response
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function documentEsignIndex(Request $request)
    {
        return view('backoffice.document-esign.index');
    }

    // public function documentEsignList(Request $request)
    // {
    //     $response_document_list = $this->eSignService->documentList($request);
    //     $response_document_list = $response_document_list->json();
    //     $data = $response_document_list['data'] ?? [];

    //     $draw = $request->input('draw', 0);
    //     $recordsTotal = $response_document_list['pagination']['total'] ?? 0;
    //     $recordsFiltered = $response_document_list['pagination']['count'] ?? 0;

    //     if(empty($data)) {
    //         return response()->json([
    //             'draw' => $draw,
    //             'recordsTotal' => $recordsTotal,
    //             'recordsFiltered' => $recordsFiltered,
    //             'data' => [],
    //         ]);
    //     }

    //     if(!$data) {
    //         return response()->json([
    //             'draw' => $request->input('draw'),
    //             'recordsTotal' => 0,
    //             'recordsFiltered' => 0,
    //             'data' => [],
    //         ]);
    //     }

    //     $request_esign = RequestEsign::with(['signers', 'surat'])
    //                                     ->whereIn('request_id', array_column($response_document_list['data'], 'id'))
    //                                     ->get()
    //                                     ->keyBy('request_id');

    //     foreach ($data as $key => $item) {
    //         $request_esign_item = $request_esign[$item['id']] ?? null;
    //         $data[$key]['surat'] = $request_esign_item->surat ?? '';
    //     }

    //     $totalRecords = count($data);
    //     $recordsFiltered = $response_document_list['pagination']['count'] ?? $totalRecords;

    //     return DataTables::of($data ?? [])
    //     ->addIndexColumn()
    //     ->addColumn('status', function($row){
    //         return status_esign($row['attributes']['signing_status'] ?? '');
    //     })
    //     ->addColumn('no_surat', function($row){
    //         return $row['surat']['no_surat'] ?? '';
    //     })
    //     ->addColumn('document', function($row){
    //         $attribute = $row['attributes'];
    //         $to_signer = implode(', ', array_column($attribute['signers'], 'name'));
    //         $html = "
    //         <div class=''>
    //             <div class='text-dark fs-14 fw-bold'>
    //                 ".$attribute['filename']." <span class='badge bg-primary ms-2'>".$attribute['category']."</span>
    //             </div>
    //             <div class='fs-12'>
    //                 Kepada : $to_signer
    //             </div>
    //         </div>
    //         ";
    //         return $html;
    //     })
    //     ->addColumn('action', function ($data) {
    //         if(empty($data['surat'])) {
    //             return '';
    //         }

    //         $action_button = " <a class='avatar-text avatar-md' href='" . route('surat-masuk.show', $data['surat']['id'] ?? '') . "'>
    //                         <i class='feather feather-eye'></i>
    //                     </a>";

    //         $action = "<div class='d-flex align-items-center gap-2'>
    //                         $action_button
    //                     </div>";

    //         return $action;
    //     })
    //     ->rawColumns(['no_surat', 'document', 'status', 'action'])
    //     ->setTotalRecords($totalRecords)
    //     ->setFilteredRecords($recordsFiltered)
    //     ->make(true);
    // }

    public function documentEsignList(Request $request)
    {
        $response_document_list = $this->eSignService->documentList($request);
        dd($response_document_list);
        $response_document_list = $response_document_list->json();
        $data = $response_document_list['data'] ?? [];

        $draw = $request->input('draw', 0);
        $recordsTotal = $response_document_list['pagination']['total'] ?? 0;
        $recordsFiltered = $response_document_list['pagination']['count'] ?? 0;

        if(empty($data)) {
            return response()->json([
                'draw' => $draw,
                'recordsTotal' => $recordsTotal,
                'recordsFiltered' => $recordsFiltered,
                'data' => [],
            ]);
        }

        Log::info('masuk sinsi pertam kali');

        $request_ids = array_column($data, 'id');
        $request_esign = RequestEsign::with(['signers', 'surat'])
                                        ->whereIn('request_id', $request_ids)
                                        ->get()
                                        ->keyBy('request_id');

        foreach ($data as $key => $item) {
            $request_esign_item = $request_esign[$item['id']] ?? null;
            $data[$key]['surat'] = $request_esign_item ? $request_esign_item->surat : null;
        }

        $datatable =  DataTables::of($data ?? [])
                        ->addIndexColumn()
                        ->addColumn('status', function($row){
                            return status_esign($row['attributes']['signing_status'] ?? '');
                        })
                        ->addColumn('no_surat', function($row){
                            return $row['surat']['no_surat'] ?? '';
                        })
                        ->addColumn('document', function($row){
                            $attribute = $row['attributes'];
                            $to_signer = implode(', ', array_column($attribute['signers'], 'name'));
                            $html = "
                            <div class=''>
                                <div class='text-dark fs-14 fw-bold'>
                                    ".$attribute['filename']." <span class='badge bg-primary ms-2'>".$attribute['category']."</span>
                                </div>
                                <div class='fs-12'>
                                    Kepada : $to_signer
                                </div>
                            </div>
                            ";
                            return $html;
                        })
                        ->addColumn('action', function ($data) {
                            $action_button = '';
                            $attribute = $data['attributes'];

                            if(empty($data['surat'])) {
                                $action_button .= "<a class='avatar-text avatar-md deleteData bg-danger text-white' href='javascript:void(0)'  data-id='".$data['id']."' data-input='" . json_encode(['id' => $data['id'], 'perihal' => $attribute['filename']]) . "'>
                                                        <i class='feather feather-trash-2'></i>
                                                    </a>";
                            }else {
                                $action_button = " <a class='avatar-text avatar-md' href='" . route('surat-masuk.show', $data['surat']['id'] ?? '') . "'>
                                                        <i class='feather feather-eye'></i>
                                                    </a>";

                                if(($attribute['signing_status'] ?? '') !== 'completed')
                                    $action_button .= "<a class='resendDocument btn bg-primary rounded btn-sm text-white' href='javascript:void(0)'  data-id='".$data['id']."' data-input='" . json_encode(['id' => $data['id'], 'perihal' => $attribute['filename']]) . "'>
                                                            <i class='feather feather-send pe-2'></i>
                                                            Kirim Ulang
                                                        </a>";
                            }

                            $action = "<div class='d-flex align-items-center gap-2'>
                                            $action_button
                                        </div>";

                            return $action;
                        })
                        ->rawColumns(['no_surat', 'document', 'status', 'action'])
                        ->with([
                            'draw' => $draw,
                            'recordsTotal' => $recordsTotal,
                            'recordsFiltered' => $recordsFiltered,
                        ])
                        ->toJson();

        return $datatable;
    }

    public function deleteDocument(string $id)
    {
        try {
            $delete = $this->eSignService->movToTrashDocument($id);
            return response()->json(['status' => true, 'message' => 'Data berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function resendDocument(string $id)
    {
        try {
            $resend = $this->eSignService->resendDocument($id);
            $resend['message'] = 'Dokumen berhasil dikirim ulang';
            return $resend;
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

}
