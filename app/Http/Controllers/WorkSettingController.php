<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Branch;
use App\Models\Employee;
use App\Models\WorkSetting;
use Illuminate\Http\Request;
use App\Models\WorkingHourSetting;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\AttendanceTypeSetting;
use App\Models\WorkingCalendarSetting;
use App\Services\LeaveCalculationService;

class WorkSettingController extends Controller
{
    protected $leaveCalculationService;

    public function __construct(LeaveCalculationService $leaveCalculationService)
    {
        $this->leaveCalculationService = $leaveCalculationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $branches = Branch::active()->get();
        $roles = Role::all();
        $attendanceTypes = AttendanceTypeSetting::all();
        $currentYear = date('Y');
        $years = range($currentYear - 2, $currentYear + 2);

        return view('backoffice.work-setting.index', compact('branches', 'roles', 'attendanceTypes', 'years', 'currentYear'));
    }

    /**
     * Get calendar data for work settings
     */
    public function getCalendarData(Request $request)
    {
        $year = $request->year ?? date('Y');
        $week = $request->week ?? date('W');
        $branchId = $request->branch_id;
        $roleId = $request->role_id;

        // Get calendar data for the week
        $calendarData = WorkingCalendarSetting::where('year', $year)
            ->where('week_number', $week)
            ->orderBy('date')
            ->get();

        // Get employees based on filters
        $employees = Employee::with(['role', 'branch'])
            ->when($branchId, function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->when($roleId, function ($q) use ($roleId) {
                $q->where('role_id', $roleId);
            })
            ->when($request->search, function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%');
            })
            ->active()
            ->get();

        // Add leave balance information and work setting requirements
        $employees = $employees->map(function ($employee) {
            // Get leave balance summary with improved CUTITAHU calculation
            $cutiTahuBalance = $this->leaveCalculationService->getCutiTahuBalance($employee);

            // Check if role needs work setting
            $workingHourSetting = WorkingHourSetting::where('role_id', $employee->role_id)->first();
            $needsWorkSetting = $workingHourSetting ? $workingHourSetting->is_need_setting_day : false;

            return [
                'id' => $employee->id,
                'nama' => $employee->nama,
                'role' => $employee->role,
                'branch' => $employee->branch,
                'leave_balance' => $cutiTahuBalance,
                'needs_work_setting' => $needsWorkSetting,
                'default_day_type' => $needsWorkSetting ? null : 'Full'
            ];
        });

        // Get existing work settings for this week
        $dates = $calendarData->pluck('date')->toArray();
        $workSettings = WorkSetting::with(['attendanceType'])
            ->whereIn('date', $dates)
            ->when($branchId, function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->when($roleId, function ($q) use ($roleId) {
                $q->where('role_id', $roleId);
            })
            ->get()
            ->groupBy(['employee_id', 'date']);

        return response()->json([
            'calendar_data' => $calendarData,
            'employees' => $employees,
            'work_settings' => $workSettings,
            'attendance_types' => AttendanceTypeSetting::all()
        ]);
    }

    /**
     * Store work settings for multiple employees and dates - IMPROVED VERSION
     */
    public function bulkStore(Request $request)
    {
        $request->validate([
            'work_settings' => 'required|array',
            'work_settings.*.employee_id' => 'required|exists:employees,id',
            'work_settings.*.date' => 'required|date',
            'work_settings.*.attendance_type_id' => 'nullable|exists:attendance_type_settings,id',
            'work_settings.*.notes' => 'nullable|string'
        ]);

        try {
            $processedCount = 0;
            $errorCount = 0;
            $affectedEmployeeIds = [];
            
            DB::transaction(function () use ($request, &$processedCount, &$errorCount, &$affectedEmployeeIds) {
                // Group settings by employee_id and date to prevent duplicates
                $groupedSettings = [];
                foreach ($request->work_settings as $setting) {
                    $key = $setting['employee_id'] . '_' . $setting['date'];
                    $groupedSettings[$key] = $setting; // This will overwrite duplicates with the last value
                    $affectedEmployeeIds[] = $setting['employee_id'];
                }

                foreach ($groupedSettings as $setting) {
                    try {
                        $employee = Employee::findOrFail($setting['employee_id']);

                        if (!empty($setting['attendance_type_id'])) {
                            // Use updateOrCreate to handle duplicates properly
                            WorkSetting::updateOrCreate(
                                [
                                    'employee_id' => $setting['employee_id'],
                                    'date' => $setting['date']
                                ],
                                [
                                    'branch_id' => $employee->branch_id,
                                    'role_id' => $employee->role_id,
                                    'attendance_type_id' => $setting['attendance_type_id'],
                                    'notes' => $setting['notes'] ?? null,
                                    'deleted_at' => null // Ensure it's not soft deleted
                                ]
                            );
                            $processedCount++;
                        } else {
                            // Remove work setting if attendance type is empty
                            $deleted = WorkSetting::where('employee_id', $setting['employee_id'])
                                ->where('date', $setting['date'])
                                ->delete();
                            if ($deleted) {
                                $processedCount++;
                            }
                        }
                    } catch (\Exception $e) {
                        $errorCount++;
                        \Log::error('Work Setting Individual Error: ' . $e->getMessage(), [
                            'setting' => $setting,
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }
            });

            // Refresh leave calculations for affected employees
            if (!empty($affectedEmployeeIds)) {
                $uniqueEmployeeIds = array_unique($affectedEmployeeIds);
                try {
                    $this->leaveCalculationService->bulkRefreshLeaveCalculations($uniqueEmployeeIds);
                } catch (\Exception $e) {
                    \Log::warning('Failed to refresh leave calculations after work setting update: ' . $e->getMessage());
                }
            }

            $message = "Pengaturan kerja berhasil disimpan. Diproses: {$processedCount} item";
            if ($errorCount > 0) {
                $message .= ", Error: {$errorCount} item";
            }

            return response()->json(['status' => true, 'message' => $message]);
        } catch (\Exception $e) {
            \Log::error('Work Setting Save Error: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['status' => false, 'message' => 'Gagal menyimpan pengaturan kerja: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get employees by branch and role with improved leave calculation
     */
    public function getEmployees(Request $request)
    {
        $employees = Employee::with(['role', 'branch'])
            ->when($request->branch_id, function ($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            })
            ->when($request->role_id, function ($q) use ($request) {
                $q->where('role_id', $request->role_id);
            })
            ->active()
            ->get();

        // Add leave balance information and work setting requirements
        $employees = $employees->map(function ($employee) {
            // Get leave balance summary with improved CUTITAHU calculation
            $cutiTahuBalance = $this->leaveCalculationService->getCutiTahuBalance($employee);

            // Check if role needs work setting
            $workingHourSetting = WorkingHourSetting::where('role_id', $employee->role_id)->first();
            $needsWorkSetting = $workingHourSetting ? $workingHourSetting->is_need_setting_day : false;

            return [
                'id' => $employee->id,
                'nama' => $employee->nama,
                'role' => $employee->role,
                'branch' => $employee->branch,
                'leave_balance' => $cutiTahuBalance,
                'needs_work_setting' => $needsWorkSetting,
                'default_day_type' => $needsWorkSetting ? null : 'Full'
            ];
        });

        return response()->json($employees);
    }

    /**
     * Get attendance types list
     */
    public function getAttendanceTypes()
    {
        $attendanceTypes = AttendanceTypeSetting::select('id', 'name', 'code')->get();
        return response()->json($attendanceTypes);
    }

    /**
     * Refresh leave calculations for specific employees
     */
    public function refreshLeaveCalculations(Request $request)
    {
        $request->validate([
            'employee_ids' => 'nullable|array',
            'employee_ids.*' => 'exists:employees,id'
        ]);

        try {
            $results = $this->leaveCalculationService->bulkRefreshLeaveCalculations($request->employee_ids);
            
            return response()->json([
                'status' => true,
                'message' => 'Perhitungan cuti berhasil diperbarui',
                'results' => $results
            ]);
        } catch (\Exception $e) {
            \Log::error('Leave Calculation Refresh Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Gagal memperbarui perhitungan cuti: ' . $e->getMessage()
            ], 500);
        }
    }
}
