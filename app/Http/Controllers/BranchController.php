<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class BranchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.branch.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backoffice.branch.create-update');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('branches', 'name')->ignore($id)->whereNull('deleted_at'),
            ],
            'code' => [
                'required',
                'string',
                'max:255',
                Rule::unique('branches', 'code')->ignore($id)->whereNull('deleted_at'),
            ],
            'status' => 'required|in:active,inactive',
            'max_off_count' => 'nullable|integer|min:0',
            'approval_levels' => 'nullable|array',
            'approval_levels.*' => 'nullable|string|exists:users,id',
        ];

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $input = [
                'name' => $request->name,
                'code' => $request->code,
                'status' => $request->status,
                'max_off_count' => $request->max_off_count ?? 0,
                'approval_levels' => $request->approval_levels ? array_filter($request->approval_levels) : null,
            ];

            Branch::updateOrCreate([
                'id' => $id,
            ], $input);
        });

        return redirect(route('branch.index'))->with('success', 'Cabang berhasil disimpan');
    }

    /**
     * Generate unique branch code from name
     */
    private function generateBranchCode($name, $excludeId = null)
    {
        $baseCode = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $name));
        $baseCode = substr($baseCode, 0, 8); // Limit to 8 characters

        if (empty($baseCode)) {
            $baseCode = 'BRANCH';
        }

        $code = $baseCode;
        $counter = 1;

        // Check for uniqueness and add counter if needed
        while (Branch::where('code', $code)
                     ->when($excludeId, function($query) use ($excludeId) {
                         return $query->where('id', '!=', $excludeId);
                     })
                     ->whereNull('deleted_at')
                     ->exists()) {
            $code = $baseCode . str_pad($counter, 2, '0', \STR_PAD_LEFT);
            $counter++;
        }

        return $code;
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $data = Branch::findOrFail($id);
        return view('backoffice.branch.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = Branch::findOrFail($id);
        return view('backoffice.branch.create-update', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $branch = Branch::findOrFail($id);

            // Check if branch has users
            if ($branch->userAccessBranches()->count() > 0) {
                return response()->json([
                    'status' => false,
                    'message' => 'Cabang tidak dapat dihapus karena masih memiliki pengguna yang terkait'
                ], 400);
            }

            DB::transaction(function () use ($branch) {
                $branch->delete();
            });

            return response()->json(['status' => true, 'message' => 'Data berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = Branch::select(
            'id',
            'name',
            'code',
            'status',
            'max_off_count',
            'approval_levels',
            'created_at'
        )
            ->latest()
            ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('status_badge', function ($data) {
                $badgeClass = $data->status === 'active' ? 'bg-success' : 'bg-danger';
                $statusText = $data->status === 'active' ? 'Aktif' : 'Tidak Aktif';
                return "<span class='badge {$badgeClass} px-3 py-2 rounded-full'>{$statusText}</span>";
            })
            ->addColumn('approval_levels_display', function ($data) {
                if (empty($data->approval_levels)) {
                    return '<span class="text-muted">Tidak ada approval</span>';
                }

                $approvalLevels = is_string($data->approval_levels) ? json_decode($data->approval_levels, true) : $data->approval_levels;
                $userIds = array_filter($approvalLevels);

                if (empty($userIds)) {
                    return '<span class="text-muted">Tidak ada approval</span>';
                }

                $users = User::whereIn('id', $userIds)->pluck('nama', 'id');
                $approvalText = [];

                foreach ($userIds as $index => $userId) {
                    if (isset($users[$userId])) {
                        $approvalText[] = ($index + 1) . '. ' . $users[$userId];
                    }
                }

                return implode('<br>', $approvalText);
            })
            ->addColumn('action', function ($data) {
                $action_button = '';

                if (canPermission('Master Cabang.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item' href='" . route('branch.edit', $data->id) . "'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>";
                }

                if (canPermission('Master Cabang.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='" . json_encode($data) . "'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action', 'status_badge', 'approval_levels_display'])
            ->smart(true)
            ->make(true);
    }

    /**
     * Get list of active branches for select options
     */
    public function list(Request $request)
    {
        $branches = Branch::select('id', 'name', 'code')
            ->active()
            ->when($request->keyword, function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->keyword . '%')
                    ->orWhere('code', 'like', '%' . $request->keyword . '%');
            })
            ->orderBy('name')
            ->get();

        return response()->json($branches);
    }

    /**
     * Check if branch code is available
     */
    public function checkCode(Request $request)
    {
        $code = $request->code;
        $id = $request->id;

        $exists = Branch::where('code', $code)
            ->when($id, function($query) use ($id) {
                return $query->where('id', '!=', $id);
            })
            ->whereNull('deleted_at')
            ->exists();

        return response()->json(['available' => !$exists]);
    }

    /**
     * Get users for approval levels dropdown
     */
    public function getUsers(Request $request)
    {
        $users = User::select('id', 'nama')
            ->where('status', 'active')
            ->when($request->keyword, function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->keyword . '%');
            })
            ->orderBy('nama')
            ->get();

        return response()->json($users);
    }
}
