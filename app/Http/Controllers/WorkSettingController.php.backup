<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Branch;
use App\Models\Employee;
use App\Models\WorkSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\AttendanceTypeSetting;
use App\Models\WorkingCalendarSetting;
use App\Models\WorkingHourSetting;

class WorkSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $branches = Branch::active()->get();
        $roles = Role::all();
        $attendanceTypes = AttendanceTypeSetting::all();
        $currentYear = date('Y');
        $years = range($currentYear - 2, $currentYear + 2);

        return view('backoffice.work-setting.index', compact('branches', 'roles', 'attendanceTypes', 'years', 'currentYear'));
    }

    /**
     * Get calendar data for work settings
     */
    public function getCalendarData(Request $request)
    {
        $year = $request->year ?? date('Y');
        $week = $request->week ?? date('W');
        $branchId = $request->branch_id;
        $roleId = $request->role_id;

        // Get calendar data for the week
        $calendarData = WorkingCalendarSetting::where('year', $year)
            ->where('week_number', $week)
            ->orderBy('date')
            ->get();

        // Get employees based on filters
        $employees = Employee::with(['role', 'branch'])
            ->when($branchId, function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->when($roleId, function ($q) use ($roleId) {
                $q->where('role_id', $roleId);
            })
            ->when($request->search, function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%');
            })
            ->active()
            ->get();

        // Add leave balance information and work setting requirements
        $employees = $employees->map(function ($employee) {
            // Get leave balance summary
            $leaveSummary = $employee->getLeaveEntitlementsSummary();
            $totalAllocated = collect($leaveSummary)->sum('allocated');
            $totalUsed = collect($leaveSummary)->sum('used');
            $totalRemaining = collect($leaveSummary)->sum('remaining');

            // Check if role needs work setting
            $workingHourSetting = WorkingHourSetting::where('role_id', $employee->role_id)->first();
            $needsWorkSetting = $workingHourSetting ? $workingHourSetting->is_need_setting_day : false;

            return [
                'id' => $employee->id,
                'nama' => $employee->nama,
                'role' => $employee->role,
                'branch' => $employee->branch,
                'leave_balance' => [
                    'jatah_cuti' => $totalAllocated,
                    'cuti_terpakai' => $totalUsed,
                    'saldo_cuti' => $totalRemaining,
                ],
                'needs_work_setting' => $needsWorkSetting,
                'default_day_type' => $needsWorkSetting ? null : 'Full'
            ];
        });

        // Get existing work settings for this week
        $dates = $calendarData->pluck('date')->toArray();
        $workSettings = WorkSetting::with(['attendanceType'])
            ->whereIn('date', $dates)
            ->when($branchId, function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->when($roleId, function ($q) use ($roleId) {
                $q->where('role_id', $roleId);
            })
            ->get()
            ->groupBy(['employee_id', 'date']);

        return response()->json([
            'calendar_data' => $calendarData,
            'employees' => $employees,
            'work_settings' => $workSettings,
            'attendance_types' => AttendanceTypeSetting::all()
        ]);
    }

    /**
     * Store work settings for multiple employees and dates
     */
    public function bulkStore(Request $request)
    {
        $request->validate([
            'work_settings' => 'required|array',
            'work_settings.*.employee_id' => 'required|exists:employees,id',
            'work_settings.*.date' => 'required|date',
            'work_settings.*.attendance_type_id' => 'nullable|exists:attendance_type_settings,id',
            'work_settings.*.notes' => 'nullable|string'
        ]);

        try {
            DB::transaction(function () use ($request) {
                foreach ($request->work_settings as $setting) {
                    if (!empty($setting['attendance_type_id'])) {
                        $employee = Employee::findOrFail($setting['employee_id']);

                        // First, try to find existing record including soft deleted ones
                        $existingRecord = WorkSetting::withTrashed()
                            ->where('employee_id', $setting['employee_id'])
                            ->where('date', $setting['date'])
                            ->first();

                        if ($existingRecord) {
                            // Update existing record and restore if soft deleted
                            $existingRecord->update([
                                'branch_id' => $employee->branch_id,
                                'role_id' => $employee->role_id,
                                'attendance_type_id' => $setting['attendance_type_id'],
                                'notes' => $setting['notes'] ?? null,
                                'deleted_at' => null // Restore if soft deleted
                            ]);
                        } else {
                            // Create new record
                            WorkSetting::create([
                                'employee_id' => $setting['employee_id'],
                                'date' => $setting['date'],
                                'branch_id' => $employee->branch_id,
                                'role_id' => $employee->role_id,
                                'attendance_type_id' => $setting['attendance_type_id'],
                                'notes' => $setting['notes'] ?? null
                            ]);
                        }
                    } else {
                        // Remove work setting if attendance type is empty
                        WorkSetting::where('employee_id', $setting['employee_id'])
                            ->where('date', $setting['date'])
                            ->delete();
                    }
                }
            });

            return response()->json(['status' => true, 'message' => 'Pengaturan kerja berhasil disimpan']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => 'Gagal menyimpan pengaturan kerja: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get employees by branch and role
     */
    public function getEmployees(Request $request)
    {
        $employees = Employee::with(['role', 'branch'])
            ->when($request->branch_id, function ($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            })
            ->when($request->role_id, function ($q) use ($request) {
                $q->where('role_id', $request->role_id);
            })
            ->active()
            ->get();

        // Add leave balance information and work setting requirements
        $employees = $employees->map(function ($employee) {
            // Get leave balance summary
            $leaveSummary = $employee->getLeaveEntitlementsSummary();
            $totalAllocated = collect($leaveSummary)->sum('allocated');
            $totalUsed = collect($leaveSummary)->sum('used');
            $totalRemaining = collect($leaveSummary)->sum('remaining');

            // Check if role needs work setting
            $workingHourSetting = WorkingHourSetting::where('role_id', $employee->role_id)->first();
            $needsWorkSetting = $workingHourSetting ? $workingHourSetting->is_need_setting_day : false;

            return [
                'id' => $employee->id,
                'nama' => $employee->nama,
                'role' => $employee->role,
                'branch' => $employee->branch,
                'leave_balance' => [
                    'jatah_cuti' => $totalAllocated,
                    'cuti_terpakai' => $totalUsed,
                    'saldo_cuti' => $totalRemaining,
                ],
                'needs_work_setting' => $needsWorkSetting,
                'default_day_type' => $needsWorkSetting ? null : 'Full'
            ];
        });

        return response()->json($employees);
    }

    /**
     * Get attendance types list
     */
    public function getAttendanceTypes()
    {
        $attendanceTypes = AttendanceTypeSetting::select('id', 'name', 'code')->get();
        return response()->json($attendanceTypes);
    }
}
