<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Branch;
use App\Models\UserAccessBranch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class UserAccessBranchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $users = User::select('id', 'nama')->orderBy('nama')->get();
        $branches = Branch::select('id', 'name')->active()->orderBy('name')->get();
        return view('backoffice.user-access-branch.index', compact('users', 'branches'));
    }

    /**
     * Store user access to branches
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'branch_ids' => 'required|array',
            'branch_ids.*' => 'exists:branches,id',
        ]);

        DB::transaction(function () use ($request) {
            $userId = $request->user_id;
            $branchIds = $request->branch_ids;

            // Delete existing access
            UserAccessBranch::where('user_id', $userId)->delete();

            // Create new access
            foreach ($branchIds as $branchId) {
                UserAccessBranch::create([
                    'user_id' => $userId,
                    'branch_id' => $branchId,
                ]);
            }
        });

        return response()->json([
            'status' => true,
            'message' => 'Akses cabang berhasil disimpan'
        ]);
    }

    /**
     * Get user access branches
     */
    public function getUserAccess(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        $userAccess = UserAccessBranch::where('user_id', $request->user_id)
                                     ->with('branch:id,name,code')
                                     ->get()
                                     ->pluck('branch_id')
                                     ->toArray();

        return response()->json($userAccess);
    }

    /**
     * Remove user access to specific branch
     */
    public function removeAccess(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'branch_id' => 'required|exists:branches,id',
        ]);

        try {
            UserAccessBranch::where('user_id', $request->user_id)
                           ->where('branch_id', $request->branch_id)
                           ->delete();

            return response()->json([
                'status' => true,
                'message' => 'Akses cabang berhasil dihapus'
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage()
            ], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = User::select(
            'id',
            'nama',
            'email'
        )
        ->with(['userAccessBranches.branch:id,name,code'])
        ->when($request->keyword, function($q) use ($request) {
            $q->where('nama', 'like', '%'.$request->keyword.'%')
              ->orWhere('email', 'like', '%'.$request->keyword.'%');
        })
        ->latest();

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('branches', function ($data) {
                $branches = $data->userAccessBranches->map(function($access) {
                    return $access->branch ? $access->branch->name : '';
                })->filter()->toArray();
                
                if (empty($branches)) {
                    return '<span class="badge badge-soft-secondary">Tidak ada akses</span>';
                }
                
                $badgeHtml = '';
                foreach ($branches as $branch) {
                    $badgeHtml .= '<span class="badge badge-soft-primary me-1">' . $branch . '</span>';
                }
                
                return $badgeHtml;
            })
            ->addColumn('branch_count', function ($data) {
                return $data->userAccessBranches->count();
            })
            ->addColumn('action', function ($data) {
                $action_button = '';
                
                if(canPermission('User Access Branch.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item editAccess' href='javascript:void(0)' data-user-id='{$data->id}' data-user-name='{$data->nama}'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit Akses</span>
                                            </a>
                                        </li>";
                }

                if(canPermission('User Access Branch.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item removeAllAccess' href='javascript:void(0)' data-user-id='{$data->id}' data-user-name='{$data->nama}'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Hapus Semua Akses</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action', 'branches'])
            ->smart(true)
            ->make(true);
    }

    /**
     * Get branches accessible by user
     */
    public function getUserBranches(Request $request)
    {
        $userId = $request->user_id ?: auth()->id();
        
        $branches = Branch::select('id', 'name', 'code')
                         ->whereHas('userAccessBranches', function($q) use ($userId) {
                             $q->where('user_id', $userId);
                         })
                         ->active()
                         ->orderBy('name')
                         ->get();

        return response()->json($branches);
    }

    /**
     * Check if user has access to branch
     */
    public function checkAccess(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'branch_id' => 'required|exists:branches,id',
        ]);

        $hasAccess = UserAccessBranch::where('user_id', $request->user_id)
                                   ->where('branch_id', $request->branch_id)
                                   ->exists();

        return response()->json([
            'has_access' => $hasAccess
        ]);
    }
}
