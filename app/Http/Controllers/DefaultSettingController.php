<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;

class DefaultSettingController extends Controller
{
    public function index()
    {
        $setting = Setting::where('group', 'default')->pluck('value', 'key');
        return view('backoffice.pengaturan.default.index', compact('setting'));
    }

    public function store(Request $request)
    {
        $input = $request->all();
        unset($input['_token']);
        foreach ($input as $key => $value) {
            if(!$value) continue;
            Setting::updateOrCreate(
                ['key' => $key, 'group' => 'default'],
                ['value' => $value]
            );
        }

        return redirect(route('pengaturan.default.index'))->with('success', 'Pengaturan berhasil disimpan');
    }
}
