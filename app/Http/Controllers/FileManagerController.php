<?php

namespace App\Http\Controllers;

use Mpdf\Mpdf;
use Illuminate\Http\Request;
use PhpOffice\PhpWord\IOFactory;
use App\Http\Services\SuratService;

class FileManagerController extends Controller
{
    protected $suratService;

    public function __construct(SuratService $suratService)
    {
        $this->suratService = $suratService;
    }

    public function uploadFile(Request $request)
    {
        $request_file = $request->file('file');
        if($request_file) {
            $file = upload_file($request_file, 'file-manager', 'content');
            
            $mimeType = $request_file->getClientMimeType();
            $file_convert = $file;
            if (in_array($mimeType, [
                'application/msword', 
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ])) {
                $filePath = public_path($file);

                $pdfPath = $this->suratService->convertFileToPdf($filePath);
                $file_convert = $pdfPath;
            }

            $url = $file;
            return response()->json([
                'status' => true,
                'url' => "/$url",
                'name_original' => $request_file->getClientOriginalName(),
                'mime_type' => $request_file->getClientMimeType(),
                'file_convert' => "/$file_convert",
                // 'size' => $request_file->getSize() ?? 0,
            ]);
        }

        return response()->json(['status' => false, 'message' => 'File tidak ditemukan']);
    }
}
