<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\WorkSetting;
use App\Models\LeaveOffSetting;
use App\Models\AttendanceRequest;
use App\Models\AttendanceTypeSetting;

class LeaveCalculationService
{
    /**
     * Get improved leave entitlements summary with real-time usage calculation
     * Specifically handles CUTITAHU leave type with accurate usage tracking
     */
    public function getImprovedLeaveEntitlementsSummary(Employee $employee)
    {
        $entitlements = $employee->getCurrentYearLeaveEntitlements();
        $summary = [];

        foreach ($entitlements as $entitlement) {
            $leaveCode = $entitlement->leaveOffSetting->code;
            $allocatedDays = $entitlement->allocated_days;
            
            // Calculate real-time used days for CUTITAHU from attendance requests
            $usedDays = $entitlement->used_days;
            if ($leaveCode === 'CUTITAHU') {
                $usedDays = $this->calculateRealTimeLeaveUsage($employee, $leaveCode);
                
                // Update the entitlement record if there's a difference
                if ($usedDays !== $entitlement->used_days) {
                    $entitlement->update([
                        'used_days' => $usedDays,
                        'remaining_days' => $allocatedDays - $usedDays
                    ]);
                }
            }
            
            $remainingDays = max(0, $allocatedDays - $usedDays);

            $summary[] = [
                'type' => $entitlement->leaveOffSetting->name,
                'code' => $leaveCode,
                'allocated' => $allocatedDays,
                'used' => $usedDays,
                'remaining' => $remainingDays,
            ];
        }

        return $summary;
    }

    /**
     * Calculate real-time leave usage from approved attendance requests
     */
    public function calculateRealTimeLeaveUsage(Employee $employee, $leaveCode)
    {
        // Get attendance types that reduce leave days
        $attendanceTypes = AttendanceTypeSetting::where('reduction_days_leave', '>', 0)->get();
        
        $totalUsedDays = 0;
        $currentYear = now()->year;

        // Calculate from approved attendance requests
        $approvedRequests = AttendanceRequest::where('employee_id', $employee->id)
            ->where('status', 'approved')
            ->whereYear('attendance_date', $currentYear)
            ->whereIn('attendance_type_id', $attendanceTypes->pluck('id'))
            ->with('attendanceType')
            ->get();

        foreach ($approvedRequests as $request) {
            if ($request->attendanceType && $request->attendanceType->reduction_days_leave > 0) {
                $totalUsedDays += $request->attendanceType->reduction_days_leave;
            }
        }

        // Also calculate from work settings that might affect leave
        $workSettings = WorkSetting::where('employee_id', $employee->id)
            ->whereYear('date', $currentYear)
            ->whereIn('attendance_type_id', $attendanceTypes->pluck('id'))
            ->with('attendanceType')
            ->get();

        foreach ($workSettings as $setting) {
            if ($setting->attendanceType && $setting->attendanceType->reduction_days_leave > 0) {
                $totalUsedDays += $setting->attendanceType->reduction_days_leave;
            }
        }

        return $totalUsedDays;
    }

    /**
     * Get CUTITAHU specific leave balance for an employee
     */
    public function getCutiTahuBalance(Employee $employee)
    {
        $leaveSummary = $this->getImprovedLeaveEntitlementsSummary($employee);
        $cutiTahuData = collect($leaveSummary)->firstWhere('code', 'CUTITAHU');
        
        return [
            'jatah_cuti' => $cutiTahuData['allocated'] ?? 0,
            'cuti_terpakai' => $cutiTahuData['used'] ?? 0,
            'saldo_cuti' => $cutiTahuData['remaining'] ?? 0,
        ];
    }

    /**
     * Refresh all leave calculations for an employee
     */
    public function refreshEmployeeLeaveCalculations(Employee $employee)
    {
        $entitlements = $employee->getCurrentYearLeaveEntitlements();
        
        foreach ($entitlements as $entitlement) {
            $leaveCode = $entitlement->leaveOffSetting->code;
            
            if ($leaveCode === 'CUTITAHU') {
                $realTimeUsed = $this->calculateRealTimeLeaveUsage($employee, $leaveCode);
                $entitlement->update([
                    'used_days' => $realTimeUsed,
                    'remaining_days' => $entitlement->allocated_days - $realTimeUsed
                ]);
            }
        }
        
        return $this->getImprovedLeaveEntitlementsSummary($employee);
    }

    /**
     * Bulk refresh leave calculations for multiple employees
     */
    public function bulkRefreshLeaveCalculations($employeeIds = null)
    {
        $employees = $employeeIds 
            ? Employee::whereIn('id', $employeeIds)->get()
            : Employee::active()->get();
            
        $results = [];
        
        foreach ($employees as $employee) {
            try {
                $results[$employee->id] = $this->refreshEmployeeLeaveCalculations($employee);
            } catch (\Exception $e) {
                \Log::error("Failed to refresh leave calculations for employee {$employee->id}: " . $e->getMessage());
                $results[$employee->id] = ['error' => $e->getMessage()];
            }
        }
        
        return $results;
    }
}
