<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendEmailSurat extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $data;
    public $subject;
    public $user;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data, $subject, $user)
    {
        $this->data = $data;
        $this->subject = $subject;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->subject)
                    ->view('backoffice.email.surat-notification')
                    ->with([
                        'data' => $this->data,
                        'user' => $this->user
                    ]);
    }
}
