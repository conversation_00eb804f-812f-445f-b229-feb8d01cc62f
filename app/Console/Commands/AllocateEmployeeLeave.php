<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\LeaveAllocationService;

class AllocateEmployeeLeave extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leave:allocate {--year= : The year to allocate leave for} {--refresh : Refresh existing allocations}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allocate annual leave entitlements for employees based on their service length and leave settings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $year = $this->option('year') ?? now()->year;
        $refresh = $this->option('refresh');

        $this->info("Starting leave allocation for year {$year}...");

        $leaveService = new LeaveAllocationService();

        try {
            if ($refresh) {
                $this->info("Refreshing existing allocations...");
                $results = $leaveService->refreshLeaveAllocations($year);
            } else {
                $results = $leaveService->allocateLeaveForAllEmployees($year);
            }

            $totalEmployees = count($results);
            $totalAllocations = array_sum(array_map('count', $results));

            $this->info("Leave allocation completed successfully!");
            $this->info("Processed {$totalEmployees} employees");
            $this->info("Created/updated {$totalAllocations} leave entitlements");

            // Show summary
            $this->table(
                ['Employee ID', 'Allocations Count'],
                collect($results)->map(function($allocations, $employeeId) {
                    return [$employeeId, count($allocations)];
                })->toArray()
            );

        } catch (\Exception $e) {
            $this->error("Error during leave allocation: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
