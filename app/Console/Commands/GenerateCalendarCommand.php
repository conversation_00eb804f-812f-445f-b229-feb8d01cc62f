<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WorkingCalendarSetting;
use Carbon\Carbon;

class GenerateCalendarCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calendar:generate {year?} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate working calendar for a specific year';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $year = $this->argument('year') ?: date('Y');
        $force = $this->option('force');

        $this->info("Generating calendar for year {$year}...");

        // Check if data already exists
        if (!$force) {
            $existing = WorkingCalendarSetting::where('year', $year)->count();
            if ($existing > 0) {
                $this->warn("Calendar data for year {$year} already exists. Use --force to overwrite.");
                return;
            }
        }

        // Delete existing data if force
        if ($force) {
            WorkingCalendarSetting::where('year', $year)->delete();
            $this->info("Deleted existing calendar data for year {$year}");
        }

        // Generate calendar data
        // Setiap minggu tepat 7 hari: Week 1 = 1-7 Jan, Week 2 = 8-14 Jan, dst
        $startDate = Carbon::createFromDate($year, 1, 1);
        $endDate = Carbon::createFromDate($year, 12, 31);

        $currentDate = $startDate->copy();
        $dayCount = 0;

        while ($currentDate <= $endDate) {
            // Calculate week number (setiap 7 hari = minggu baru)
            // Day 0-6 = Week 1, Day 7-13 = Week 2, dst
            $weekNumber = intval($dayCount / 7) + 1;

            // Tentukan day_type berdasarkan hari dalam seminggu
            $dayOfWeek = $currentDate->dayOfWeek; // 0=Sunday, 1=Monday, ..., 6=Saturday
            $isWeekend = ($dayOfWeek == 0 || $dayOfWeek == 6); // Sunday atau Saturday
            $dayType = $isWeekend ? 'weekend' : 'weekdays';

            WorkingCalendarSetting::create([
                'date' => $currentDate->format('Y-m-d'),
                'day_name' => $currentDate->translatedFormat('l'),
                'week_number' => $weekNumber,
                'day_type' => $dayType,
                'is_holiday' => false,
                'year' => $currentDate->year,
                'month' => $currentDate->month,
            ]);

            $currentDate->addDay();
            $dayCount++;
        }

        $totalWeeks = $weekNumber;
        $this->info("Successfully generated calendar for year {$year}");
        $this->info("Total days: {$dayCount}");
        $this->info("Total weeks: {$totalWeeks}");
        $this->info("Week 1 starts from: {$startDate->format('Y-m-d')} ({$startDate->translatedFormat('l')})");
    }
}
